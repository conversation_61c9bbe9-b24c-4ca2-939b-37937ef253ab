package com.asmtunis.procaisseinventory.auth.base_config.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.BASECONFIG_TABLE
import kotlinx.coroutines.flow.Flow

@Dao
interface BaseConfigDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun upsert(fidcardEntity: BaseConfig) // : Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun upsertAll(fidcardEntity: List<BaseConfig>) // : Long

    // @Query("UPDATE $BASECONFIG_TABLE SET selected = :selected WHERE id_base_config = :idbaseconfig")
    // fun updateISSelected(selected: <PERSON><PERSON><PERSON>,idbaseconfig:String)

    @Query("SELECT * FROM $BASECONFIG_TABLE")
    fun get(): Flow<List<BaseConfig>>

    @Query("SELECT * FROM $BASECONFIG_TABLE  WHERE id_base_config Like :idbaseconfig and password Like :password")
    fun getById(idbaseconfig: String, password: String): Flow<BaseConfig>


    @Query("DELETE FROM $BASECONFIG_TABLE WHERE produit Like :produit ")
    fun deleteByProduit(produit : String)

    @Query("DELETE FROM $BASECONFIG_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $BASECONFIG_TABLE WHERE id_base_config = :value") // you can use this too, for delete note by id.
    fun deleteFidCardById(value: String)
}
