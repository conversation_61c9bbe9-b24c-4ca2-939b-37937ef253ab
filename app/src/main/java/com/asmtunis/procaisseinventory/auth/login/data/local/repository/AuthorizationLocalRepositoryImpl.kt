package com.asmtunis.procaisseinventory.auth.login.data.local.repository

import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.login.data.local.dao.AuthorizationDAO
import kotlinx.coroutines.flow.Flow



class AuthorizationLocalRepositoryImpl(
    private val authorizationDAO: AuthorizationDAO
) : AuthorizationLocalRepository {


    override fun upsertAll(value: List<Authorization>) = authorizationDAO.insert(value)

    override fun deleteAll() = authorizationDAO.deleteAll()

    override fun getAll(): Flow<List<Authorization>?> = authorizationDAO.all

}