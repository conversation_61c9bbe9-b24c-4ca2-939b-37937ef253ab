package com.asmtunis.procaisseinventory.auth.login.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.coroutines.flow.Flow


@Dao
interface AuthorizationDAO {
    @get:Query("SELECT * FROM ${ProCaisseConstants.AUTHORIZATION_TABLE}")
    val all: Flow<List<Authorization>?>


    @Query("DELETE FROM ${ProCaisseConstants.AUTHORIZATION_TABLE}")
    fun deleteAll()



    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: List<Authorization>)

}