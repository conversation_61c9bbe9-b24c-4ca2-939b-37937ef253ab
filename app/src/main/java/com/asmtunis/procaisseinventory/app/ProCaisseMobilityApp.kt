package com.asmtunis.procaisseinventory.app

import android.annotation.SuppressLint
import android.app.Application
import android.provider.Settings
import android.util.Log
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.google.firebase.Firebase
import com.google.firebase.crashlytics.crashlytics
import com.sunmi.printerx.PrinterSdk
import dagger.hilt.android.HiltAndroidApp
import javax.inject.Inject


@HiltAndroidApp
class ProCaisseMobilityApp : Application(), Configuration.Provider{

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    @SuppressLint("HardwareIds")
    override fun onCreate() {
        super.onCreate()
        // Set device ID
        DEVICE_ID = Settings.Secure.getString(
            <EMAIL>,
            Settings.Secure.ANDROID_ID
        )

        Firebase.crashlytics.setUserId(DEVICE_ID)

        // Initialize Sunmi printer SDK early in the app lifecycle
        try {
            // Enable SDK logging for debugging
            PrinterSdk.getInstance().log(true, null)
            Log.d("ProCaisseMobilityApp", "Sunmi SDK logging enabled")

            // Initialize the Sunmi Printer SDK
            PrinterSdk.getInstance().getPrinter(applicationContext, object : PrinterSdk.PrinterListen {
                override fun onDefPrinter(printer: PrinterSdk.Printer?) {
                    Log.d("ProCaisseMobilityApp", "onDefPrinter called with printer: $printer")
                    SunmiPrintManager.selectPrinter = printer  // Set the global printer instance

                    if (printer != null) {
                        Log.d("ProCaisseMobilityApp", "Sunmi printer initialized successfully in Application")
                    } else {
                        Log.d("ProCaisseMobilityApp", "No Sunmi printer found in Application initialization")
                    }
                }

                override fun onPrinters(printers: MutableList<PrinterSdk.Printer>?) {
                    Log.d("ProCaisseMobilityApp", "onPrinters called with printers: ${printers?.size ?: 0}")
                }
            })
            Log.d("ProCaisseMobilityApp", "Sunmi printer initialization requested in Application")
        } catch (e: Exception) {
            Log.e("ProCaisseMobilityApp", "Error initializing Sunmi printer in Application: ${e.message}")
            e.printStackTrace()
        }
    }

    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setWorkerFactory(workerFactory)
            .build()
}
