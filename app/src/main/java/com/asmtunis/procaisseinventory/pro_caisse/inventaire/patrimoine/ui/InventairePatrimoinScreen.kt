package com.asmtunis.procaisseinventory.pro_caisse.inventaire.patrimoine.ui

import NavDrawer
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.InventairePatrimoineDetailRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.StringUtils.getClientName
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.Constants.PATRIMOINE
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePat
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.TypePatrimoine
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.view_model.InventaireViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.CustomModalBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.ListItem
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.*
import com.simapps.ui_kit.CustomScrollableTabRow
import kotlinx.coroutines.launch


@Composable
fun InventairePatrimoineScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    settingViewModel: SettingViewModel,
    navDrawerViewModel: NavigationDrawerViewModel,
    mainViewModel : MainViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    invPatViewModel: InventaireViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,

    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
) {
    val uiWindowState = settingViewModel.uiWindowState

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)

    val scope = rememberCoroutineScope()

    val clientList = mainViewModel.clientList
    val clientByCode = clientList.firstOrNull{ it.cLICode == clientId }?: Client()//mainViewModel.clientByCode

    val utilisateur = mainViewModel.utilisateur

    val context = LocalContext.current
    val state = invPatViewModel.invPatrimoineListstate
    val listOrder = state.listOrder
    val listFilter = state.search
    val filterList = context.resources.getStringArray(R.array.inventaire_filter)
    val selectedInvPatrimoineWithLigne = invPatViewModel.selectedInvPatrimoineWithLigne
    val selectedInvPatrimoine = invPatViewModel.selectedInvPatrimoine
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val filteredDepOutList = invPatViewModel.filteredDepOutList
    val filteredDepInList = invPatViewModel.filteredDepInList
    val filteredInventaireList = invPatViewModel.filteredInventaireList
    val filteredAffectationList = invPatViewModel.filteredAffectationList
    val invPatListNotFiltered = invPatViewModel.invPatListNotFiltered

    //todo see if room query for below affect app performance
    val depOutListNotFiltred = invPatViewModel.depOutListNotFiltred
    val depInListNotFiltred = invPatViewModel.depInListNotFiltred
    val inventaireListNotFiltred = invPatViewModel.inventaireListNotFiltred
    val affectationListNotFiltred = invPatViewModel.affectationListNotFiltred


    val syncInvPatrimoineViewModel = syncProcaisseViewModels.syncInvPatrimoineViewModel

    val tabRowItems = getPatrimoineTabRowItems(
        navigate = { navigate(it) },
        depOutListNotFiltred = depOutListNotFiltred,
        depInListNotFiltred = depInListNotFiltred,
        inventaireListNotFiltred = inventaireListNotFiltred,
        affectationListNotFiltred = affectationListNotFiltred,
        filteredDepOutList = filteredDepOutList,
        filteredDepInList = filteredDepInList,
        filteredInventaireList = filteredInventaireList,
        filteredAffectationList = filteredAffectationList,
        invPatViewModel = invPatViewModel,
        selectArtMobilityVM = selectArtMobilityVM,
        getClientName = { cltName, cltCode ->
            getClientName(cltName = cltName, cltCode = cltCode)
        }
    )
    val pagerState = rememberPagerState(
        initialPage = 0,
        initialPageOffsetFraction = 0f
    ) {
        // provide pageCount
        tabRowItems.size
    }


    LaunchedEffect(key1 = clientByCode) {
        // Log.d("junbvas",invPatViewModel.invPatrimoineListstate.filterByTypeInventaire)

        invPatViewModel.onSearchValueChange(TextFieldValue(clientByCode.cLICode))
        invPatViewModel.onEvent(
            event = ListEvent.ListSearch(ListSearch.SecondSearch()),
            utilisateur = utilisateur,
            devEtat = PATRIMOINE
        )
    }

    LaunchedEffect(key1 = invPatViewModel.searchTextState.text, key2 = state.lists, key3 = state.search) {

        invPatViewModel.filterInvPatrimoine(
            from = "2",
            invePatrimoineFilterListState = state,
            utilisateur = utilisateur,
            devEtat = PATRIMOINE
        )
    }

    /*  LaunchedEffect(key1 = Unit){
         invPatViewModel.getInvPatList(station = utilisateur.Station, devEtat = PATRIMOINE)
      }*/
    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,

        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            topBar = {
                AppBar(
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = { scope.launch { drawer.open() } },
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,                    title = stringResource(id = navDrawerViewModel.proCaisseSelectedMenu.title),
                    titleVisibilty = !invPatViewModel.showSearchView && invPatViewModel.searchTextState.text.isEmpty(),
                    actions = {
                        SearchSectionComposable(
                            label = context.getString(R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    is ListSearch.ThirdSearch -> filterList[2]
                                }),
                            searchVisibility  = invPatViewModel.showSearchView || invPatViewModel.searchTextState.text.isNotEmpty(),
                            searchTextState = invPatViewModel.searchTextState,
                            onSearchValueChange = {
                                invPatViewModel.onSearchValueChange(TextFieldValue(it))
                                if(it == "")   {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    // mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowSearchViewChange = {
                                invPatViewModel.onShowSearchViewChange(it)
                                if(!it) {
                                    /** this bloc to handle search visibility when custom search by client*/
                                    /** this bloc to handle search visibility when custom search by client*/
                                    invPatViewModel.onSearchValueChange(TextFieldValue(""))
                                    //  mainViewModel.onSelectedClientChange(Client())
                                }
                            },
                            onShowCustomFilterChange = {
                                invPatViewModel.onShowCustomFilterChange(it)
                            }
                        )


                    },
                )
            }




        ) { padding ->
            if (invPatViewModel.showCustomModalBottomSheet)
                CustomModalBottomSheet(
                    title = selectedInvPatrimoine.dEVNum,
                    remoteResponseState =  when (invPatViewModel.tabsState) {
                        TypePat.AFFECTATION.typePat -> syncInvPatrimoineViewModel.responseAddAffectationState
                        TypePat.INVENTAIRE.typePat -> syncInvPatrimoineViewModel.responseAddInventaireState
                        TypePat.DEP_IN.typePat -> syncInvPatrimoineViewModel.responseAddDepInState
                        TypePat.DEP_OUT.typePat -> syncInvPatrimoineViewModel.responseAddDepOutState
                        else -> {syncInvPatrimoineViewModel.responseAddDepOutState}
                    },
                    onDismissRequest= {
                        invPatViewModel.onShowCustomModalBottomSheetChange(false)
                    },
                    onDeleteRequest= {},
                    onPrintRequest= {},
                    status =  "" //TODO
                    /*  when (invPatViewModel.tabsState) {
                       0 ->     newProdViewModel.selectedNewProduct.status
                       1 ->   promotionViewModel.selectedPromotion.status
                       2 ->     prixViewModel.selectedPrix.status
                       3 ->  autreViewModel.selectedAutre.status
                       else -> {""}
                   }*/,
                    onSyncRequest = {
                        when (invPatViewModel.tabsState) {
                            TypePat.AFFECTATION.typePat -> syncInvPatrimoineViewModel.syncAffectation(selectedInvPatrimoineWithLigne)
                            TypePat.INVENTAIRE.typePat -> syncInvPatrimoineViewModel.syncInventaire(selectedInvPatrimoineWithLigne)
                            TypePat.DEP_IN.typePat -> syncInvPatrimoineViewModel.syncDepIn(selectedInvPatrimoineWithLigne)
                            TypePat.DEP_OUT.typePat -> syncInvPatrimoineViewModel.syncDepOut(selectedInvPatrimoineWithLigne)
                            else -> {""}
                        }
                        //   syncInvPatrimoineViewModel.syn(notSyncVisites = distNumViewModel.selectedVisiteWithLinesMap)
                    }
                )

            if (invPatViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.inventaire_order),
                    onShowCustomFilterChange  = {
                        invPatViewModel.onShowCustomFilterChange(false)
                    },
                    onEvent = {
                        invPatViewModel.onEvent(
                            event = it,
                            utilisateur = utilisateur,
                            devEtat = PATRIMOINE
                        )
                    }
                )





            }

            Column(
                modifier = Modifier.padding(padding).padding(top = 12.dp)
            ) {
                CustomScrollableTabRow(
                    pagerState = pagerState,
                    tabs = { sizeList, noInteraction->
                        tabRowItems.forEachIndexed { index, item ->
                            Tab(
                                selected = pagerState.currentPage == index,
                                onClick = {

                                    invPatViewModel.setTabState(tabs = item.id, typeInv = "")

                                    scope.launch {
                                        pagerState.animateScrollToPage(index)
                                    }
                                    when (index) {
                                        0 -> {
                                            invPatViewModel.onEvent(
                                                event = ListEvent.FirstCustomFilter(TypePatrimoine.AFFECTATION.typePat),
                                                utilisateur = utilisateur,
                                                devEtat = PATRIMOINE
                                            )
                                        }

                                        1 ->   {
                                            invPatViewModel.onEvent(
                                                event =  ListEvent.FirstCustomFilter(TypePatrimoine.INVENTAIRE.typePat),
                                                utilisateur = utilisateur,
                                                devEtat = PATRIMOINE
                                            )
                                        }
                                        2 ->     {
                                            invPatViewModel.onEvent(
                                                event = ListEvent.FirstCustomFilter(TypePatrimoine.ENTREE.typePat),
                                                utilisateur = utilisateur,
                                                devEtat = PATRIMOINE
                                            )
                                        }
                                        3 ->  {
                                            invPatViewModel.onEvent(
                                                event =   ListEvent.FirstCustomFilter(TypePatrimoine.SORTIE.typePat),
                                                utilisateur = utilisateur,
                                                devEtat = PATRIMOINE
                                            )
                                        }
                                        else -> {""}
                                    }
                                    invPatViewModel.onSearchValueChange(TextFieldValue(""))
                                },
                                modifier = Modifier
                                    .onSizeChanged {
                                        sizeList[index] = Pair(it.width.toFloat(), it.height.toFloat())
                                    },
                                interactionSource = remember { noInteraction },
                                /* icon = {
                                     Icon(imageVector = item.icon, contentDescription = "")
                                 },*/
                                text = {
                                    Text(
                                        text = item.title.uppercase(),
                                        style = TextStyle(
                                            fontWeight = FontWeight.Bold,
                                            fontSize = 12.sp,
                                        ),
                                        modifier = Modifier
                                            .align(Alignment.CenterHorizontally)
                                            .padding(horizontal = 32.dp, vertical = 16.dp)
                                    )

                                }
                            )
                        }


                    },
                    pagerContent = {
                        HorizontalPager(
                            state = pagerState,
                            beyondViewportPageCount = 10,
                        ) { page ->
                            if(getProCaisseDataViewModel.bonCommandeState.loading)
                                LottieAnim(lotti = R.raw.loading, size = 250.dp)
                            else tabRowItems[page].screen()
                        }
                    }
                )







            }
        }
    }
}


@Composable
fun InvPatList(
    navigate: (route: Any) -> Unit,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    invPatrimoineViewModel: InventaireViewModel,
    inPatList: Map<BonCommande, List<LigneBonCommande>>,
    getClientName: (cltName: String?, cltCode: String?) -> String

    //  textValidationViewModel: VcTextValidationViewModel = hiltViewModel()
) {

    /*LaunchedEffect(key1 = newProdViewModel.newProductSearchTextState.text, key2 = newProdState.lists, key3 = newProdState.search) {
        newProdViewModel.filterNewProductVC(newProdState)
    }*/

    val listState = rememberLazyListState()





    if (inPatList.isNotEmpty())
        ListInvPat(
            listState = listState,
            invPatList = inPatList,
            getClientName = { cltName,cltCode ->
                getClientName(cltName, cltCode)
            },
            onItemClick = { screen,selectedInvPat ->
                invPatrimoineViewModel.restInvPatrimoine()
                selectArtMobilityVM.resetSelectedMobilityArticles()
                invPatrimoineViewModel.onSelectedInvPatrimoineChange(selectedInvPat)
                navigate(screen)
            },
            onMoreClick = {selectedInvPat ->
                invPatrimoineViewModel.onSelectedInvPatrimoineChange(selectedInvPat)
                invPatrimoineViewModel.onShowCustomModalBottomSheetChange(true)
            }

        )

    else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
}

@Composable
fun ListInvPat(
    listState: LazyListState,
    onItemClick :(screen : Any, selectedInvPat :  Map<BonCommande, List<LigneBonCommande>>)-> Unit,
    onMoreClick :(selectedInvPat :  Map<BonCommande, List<LigneBonCommande>>)-> Unit,
    getClientName : (cltName: String?, cltCode: String?)-> String,
    invPatList: Map<BonCommande, List<LigneBonCommande>>,
){
    val bonCommande: MutableList<BonCommande> = arrayListOf()
    val ligneBonCommande: MutableList<LigneBonCommande> = arrayListOf()



    invPatList.forEach { (key, value) ->
        run {
            bonCommande.add(key)
            ligneBonCommande.addAll(value)
        }
    }


    LazyColumn(
        verticalArrangement =  Arrangement.spacedBy(12.dp),
        modifier = Modifier.fillMaxSize(),
        state = listState
    ) {
        items(
            //   items= filteredList.keys.toList()
            count = invPatList.size,
            key = {
                bonCommande[it].dEVNum

            }

        ) { index ->

            ListItem(

                onItemClick={

                    onItemClick(InventairePatrimoineDetailRoute, invPatList.filter { it.key == bonCommande[index] })


                    //  Log.d("ppmmllffvv", "pppp ${invPatList.filter { it.key == bonCommande[index] }.values.first().first().lGDEVCodeM?:"empty"}" )
                },
                firstText = bonCommande[index].dEVNum ,
                secondText = getClientName(bonCommande[index].dEVClientName,bonCommande[index].dEVCodeClient) ,

                thirdText =   "(" + invPatList[bonCommande[index]]?.size.toString()+") Article(s)" ,
                //     forthText = bonCommande[index].dEV_info3?:"N/A",
                dateText =  bonCommande[index].dEVDDm?:"N/A",
                isSync = bonCommande[index].isSync,
                status = bonCommande[index].status,
                onResetDeletedClick = {
                    //TODO
                },
                moreClickIsVisible = false,
                onMoreClick = {
                    onMoreClick(invPatList.filter { it.key == bonCommande[index] })

                },
            )



        }
    }
}





