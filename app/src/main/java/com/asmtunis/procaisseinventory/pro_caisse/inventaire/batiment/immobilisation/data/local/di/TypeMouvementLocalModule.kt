package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.dao.TypeMouvementDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.TypeMouvementLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.immobilisation.data.local.repository.TypeMouvementLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

 


@Module
@InstallIn(SingletonComponent::class)
class TypeMouvementLocalModule {

    @Provides
    @Singleton
    fun provideTypeMouvementDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.typeMouvementDao()

    @Provides
    @Singleton
    @Named("TypeMouvement")
    fun provideTypeMouvementRepository(
        typeMouvementDAO: TypeMouvementDAO
    ): TypeMouvementLocalRepository = TypeMouvementLocalRepositoryImpl(typeMouvementDAO = typeMouvementDAO)
}