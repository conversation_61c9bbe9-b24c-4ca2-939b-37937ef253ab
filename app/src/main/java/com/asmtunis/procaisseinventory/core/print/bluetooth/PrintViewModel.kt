package com.asmtunis.procaisseinventory.core.print.bluetooth

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.Typeface
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.graphics.createBitmap
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import arrow.core.getOrElse
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.BluetoothStatus
import com.asmtunis.procaisseinventory.core.enum_classes.TicketState
import com.asmtunis.procaisseinventory.core.print.PrintingData
import com.asmtunis.procaisseinventory.core.print.bluetooth.EscPosPrinterCommands.bitmapToBytes
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils
import com.asmtunis.procaisseinventory.core.utils.CalculationsUtils.calculateAmountExcludingTax
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringPlural
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.core.utils.mobilecode.MobileCodeGeneration
import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommandeWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommandeWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.LigneTicketWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.reglement.ReglementUtils.getReglementNumber
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine.ReglementCaisseWithTicketAndClient
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.BonEntree
import com.asmtunis.procaisseinventory.pro_inventory.achat.data.domaine.LigneBonEntree
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.BonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.data.domaine.LigneBonLivraison
import com.asmtunis.procaisseinventory.pro_inventory.data.fournisseur.domaine.Fournisseur
import com.asmtunis.procaisseinventory.pro_inventory.global_data_class.ProInventoryLocalDb
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.Inventaire
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.data.domaine.LigneInventaire
import com.google.zxing.BarcodeFormat
import com.google.zxing.EncodeHintType
import com.google.zxing.MultiFormatWriter
import com.google.zxing.WriterException
import com.google.zxing.common.BitMatrix
import com.simapps.escpos.bluetooth.BluetoothDevice
import com.simapps.escpos.bluetooth.BluetoothPrinterManager
import com.simapps.escpos.core.BarcodeSpec
import com.simapps.escpos.core.Charset
import com.simapps.escpos.core.CommandBuilder
import com.simapps.escpos.core.HriPosition
import com.simapps.escpos.core.LineSegment
import com.simapps.escpos.core.PrinterConfiguration
import com.simapps.escpos.core.TextAlignment
import com.simapps.escpos.core.print
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.launch
import java.util.EnumMap
import javax.inject.Inject
import kotlin.io.encoding.Base64
import kotlin.io.encoding.ExperimentalEncodingApi
import kotlin.math.abs


@OptIn(ExperimentalEncodingApi::class)
@HiltViewModel
class PrintViewModel @Inject constructor(
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val proInventoryLocalDb: ProInventoryLocalDb,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    // var proccedPrinting  by savedStateHandle.saveable { mutableStateOf(false) }
    var proccedPrinting by mutableStateOf(false)
        private set

    fun onproccedPrintingChange(state: Boolean) {
        proccedPrinting = state
    }


    private fun setHorizontalSeperator(charsPerLines: Int): String = "-".repeat(charsPerLines - 2)


    var etablisement by mutableStateOf(Etablisement())
        private set

    private fun getEtablissementList() {
        viewModelScope.launch {
            proCaisseLocalDb.etablisement.getAll().collect {

                if (it.isNullOrEmpty()) return@collect
                etablisement = it.first()
            }
        }
    }


    var parametrage by mutableStateOf(Parametrages())
        private set

    private fun getParametrages() {
        viewModelScope.launch {
            proInventoryLocalDb.parametrage.getOne().collect {


                parametrage = it ?: Parametrages()

                logo = parametrage.pARAMLogo
            }
        }
    }


    var openPrintInfoDialogue by mutableStateOf(false)
    fun onOpenPrintInfoDialogueChange(open: Boolean) {
        openPrintInfoDialogue = open
    }

    var logo by mutableStateOf("")
        private set


    init {
        getEtablissementList()
        getParametrages()
    }

    var listBluetoothDevice by mutableStateOf<List<BluetoothDevice>>(emptyList())
    var selectedDevicAdres by mutableStateOf("")


    fun getListPairedPrinters(context: Context) {
//    val device = btManager.pairedPrinters().firstOrNull()

        val btManager = BluetoothPrinterManager(context)

        val device = btManager.pairedPrinters().getOrElse { null }

        listBluetoothDevice = device?.toList() ?: emptyList()
    }


    var showList by mutableStateOf(false)
    fun onshowListChange(state: Boolean) {
        showList = state
    }


    var deviceAddress by mutableStateOf("")
    var firstTimeConnected by mutableStateOf(false) // used to auto print after first time paired
    fun onDeviceAddressChange(adress: String) {
        deviceAddress = adress
        firstTimeConnected = true
    }

    fun onFirstTimeConnectedChange(firstConnect: Boolean) {
        firstTimeConnected = firstConnect
    }

    fun printBarecode(context: Context, printParams: PrintingData) {
        viewModelScope.launch {
            val btManager = BluetoothPrinterManager(context)


            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected

            val connection =
                btManager.openConnection(device).getOrNull() //?: return MynError.Disconnected


            val config = PrinterConfiguration(charactersPerLine = 44)
            connection?.print(config) {


                val bmp = draw(
                    article = Article(
                        aRTCodeBar = "978020137962",
                        prixSolde = "12",
                        aRTDesignation = "test"
                    )
                )


                if (bmp != null) bitmap(bitmapToBytes(bitmap = bmp, height = 200))


                printLogo(printParams = printParams)

                line(" ")
                line(" ")

                // BARCODES - SUPPORTS A NUMBER OF 1D and 2D CODES
                val barecode = BarcodeSpec.EAN13Spec.create("978020137962", HriPosition.NONE)
                barecode
                    .onRight {
                        line("UPCASpec UPCASpec UPCASpec UPCASpec")
                        barcode(it)
                        line("UPCASpec V UPCASpec  V  UPCASpec  V  ")
                    }
                    .onLeft { err ->
                        line("Could not construct QR code:")
                        onPrintResultChange(err.toString())
                        line(err.toString())
                    }

                withTextSize(width = 2, height = 3) {
                    line("µ µ µ µ")
                }


            }

        }


    }


    var printResult by mutableStateOf("")
    private fun onPrintResultChange(result: String) {
        printResult = result

        if (printResult == BluetoothStatus.PRINT_SUCCESS.status) {
            onOpenPrintInfoDialogueChange(false)
        } else onOpenPrintInfoDialogueChange(true)

        onproccedPrintingChange(false)
    }

    private var btManager: BluetoothPrinterManager? by mutableStateOf(null)
    private var listBtDevice: List<BluetoothDevice>? by mutableStateOf(null)

    fun awaitPrint(context: Context, toPrint: () -> Unit) {

        printResult = BluetoothStatus.IS_PRINTING.status
        onOpenPrintInfoDialogueChange(true)
        btManager = BluetoothPrinterManager(context)
        btManager!!.pairedPrinters().onRight {
            listBtDevice = it

            if (listBtDevice!!.isEmpty()) {
                onPrintResultChange(BluetoothStatus.EMPTY_PAIRED_LIST.status)
                return@onRight
            }
            toPrint()
        }
            .onLeft { err ->
                onPrintResultChange(err.toString())
            }
    }

    /**
     *  * * * B O N ** L I V R A I S O N * * * *
     */
    fun printTicket(
        context: Context,
        ticketWithFactureAndPayments: TicketWithFactureAndPayments,
        listLigneTicket: List<LigneTicketWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData,
        prefixList: List<Prefixe>
    ) {
        viewModelScope.launch {
            val prefixeBl =
                prefixList.firstOrNull { it.pREIdTable == "Bon_livraison" }?.pREPrefixe ?: "BL_M_"

            val charPerLine = printParams.paperSize
            val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)


            val facture = ticketWithFactureAndPayments.facture
            val ticket = ticketWithFactureAndPayments.ticket
            val reglement = ticketWithFactureAndPayments.reglement
            val reglementTicketPart = ticketWithFactureAndPayments.regTicketPart
            val btManager = BluetoothPrinterManager(context)

            val isCredit = ticket!!.tIKEtat == TicketState.CREDIT.getValue()
            val client = ticketWithFactureAndPayments.client
                ?: Client(cLICode = ticket.tIKCodClt)
            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }?.firstOrNull { it.address == deviceAddress } ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected

            btManager.openConnection(device).onRight { connection ->
                    val config = PrinterConfiguration(charactersPerLine = charPerLine)
                    connection.print(config) {
                        charset(Charset.Windows1256)

                        textAlign(TextAlignment.CENTER)


                        val invoiced = !ticket.tIKNumeroBL.isNullOrBlank()

                        var numFact: String? = ""
                        if ((ticket.tIKNumeroBL != null || ticket.tIKNumFact.isNotEmpty())
                        ) {
                            if (ticket.tIKNumeroBL != null)
                                if (!ticket.tIKNumeroBL.contains("BL_M") && !ticket.tIKNumeroBL.contains(prefixeBl)) {
                                    numFact = ticket.tIKNumeroBL
                                }

                        }
                        //    val num = "Num : " + if (!invoiced) ticket.tIKNumTicketM else numFact
                        val num = "Num : " + if (!invoiced) prefixeBl + ticket.tIKNumTicket else numFact


                        /*  if (num.contains("_") && !invoiced) {
                              val numTicket = num.split("_".toRegex()).dropLastWhile { it.isEmpty() }
                                  .toTypedArray()
                             num = "BL_M_" + numTicket[2] + "_" + numTicket[3] +"_" + numTicket[5]
                          }*/

                        val title = if (invoiced) "Facture" else "Bon de Livraison"
                        bold(true)
                        line(title)
                        bold(false)
                        line(num)

                        if (isCredit)
                            line("(Credit)")

                        if (isCredit && ticketWithFactureAndPayments.regTicketPart != null)
                            line("(Paiement Partiel)")
                        //HERE REG PARTIEL
                        line("Date: " + ticket.tIKDateHeureTicket.substringBefore("."))



                        printLogo(printParams = printParams)



                        printEtablismentInfo(
                            charPerLine = charPerLine,
                            printingData = printParams
                        )




                        printClientInfo(
                            charPerLine = charPerLine,
                            client = client,
                            tIKNomClient = ticket.tIKNomClient,
                            tIKCodClt = ticket.tIKCodClt,
                            clientAdress = ticket.tIKEmplacementMariage?: "N/A"
                        )

                        printClientInfo(
                            charPerLine = charPerLine,
                            idSCaisse = ticket.tIKIdSCaisse,
                            utilisateur = utilisateur
                        )
                        // MULTIPLE TEXT ALIGNMENTS PER LINE


                        bold(true)
                        if(printParams.taxeArticle) {
                            segmentedLine(
                                LineSegment("QTE", TextAlignment.LEFT),
                                LineSegment("P.U", TextAlignment.CENTER),
                                LineSegment("TOTAL HT", TextAlignment.CENTER),
                                LineSegment("TOTAL", TextAlignment.RIGHT),
                            )
                        } else {
                            segmentedLine(
                                LineSegment("QTE", TextAlignment.LEFT),
                                LineSegment("P.U", TextAlignment.CENTER),
                                LineSegment("TOTAL", TextAlignment.RIGHT),
                            )
                        }
                        // line(TABLE_HEADER_FOUR)
                        line(HORIZONTAL_SEPERATOR)
                        bold(false)
                        textAlign(TextAlignment.LEFT)

                        printTicketArticleTable(ligneTicketWithArticleList = listLigneTicket, printParams = printParams)

                        bold(true)
                        line(HORIZONTAL_SEPERATOR)
                        segmentedLine(
                            LineSegment("Total Articles:", TextAlignment.LEFT),
                            LineSegment(listLigneTicket.size.toString(), TextAlignment.RIGHT),
                        )
                        withBold(false) {
                            line(HORIZONTAL_SEPERATOR)
                        }




                        val timbValue =  if(stringToDouble(facture?.factTimbre) != 0.0) {
                                 facture?.factTimbre
                        } else {
                            if (ticketWithFactureAndPayments.timbre == Timbre() || ticketWithFactureAndPayments.timbre == null)
                                ticket.tIKTimbre
                            else ticketWithFactureAndPayments.timbre?.tIMBValue ?: "0"
                        }


                        /*   val total: Double =
                               stringToDouble(ticket.tIKMtTTC) + stringToDouble(ticket.tIKMtRemise) - stringToDouble(
                                   timbValue
                               )
                           textAlign(TextAlignment.CENTER)
                           segmentedLine(
                               LineSegment("Montant Total:", TextAlignment.LEFT),
                               LineSegment(
                                   convertStringToPriceFormat(total.toString()),
                                   TextAlignment.RIGHT
                               ),
                           )*/

              val ttHt = stringToDouble(ticket.tIKMtTTC) + stringToDouble(ticket.tIKMtRemise) - stringToDouble(timbValue)- stringToDouble(ticket.tIKMtTVA)

                        val netTTC = if(facture!= null) stringToDouble(facture.factMntTTC)
                        else stringToDouble(ticket.tIKMtTTC) + stringToDouble(timbValue)




                      val totalNetHt = ttHt - stringToDouble(ticket.tIKMtRemise)

                        textAlign(TextAlignment.CENTER)
                        segmentedLine(
                            LineSegment("Total HT:", TextAlignment.LEFT),
                            LineSegment(convertStringToPriceFormat(ttHt.toString()), TextAlignment.RIGHT)
                        )

                        if(stringToDouble(ticket.tIKMtRemise) > 0.0)
                        segmentedLine(
                            LineSegment("Total Remise:", TextAlignment.LEFT),
                            LineSegment(convertStringToPriceFormat(ticket.tIKMtRemise), TextAlignment.RIGHT)
                        )

                        if(totalNetHt != ttHt)
                        segmentedLine(
                            LineSegment("Total Net HT:", TextAlignment.LEFT),
                            LineSegment(
                                text = convertStringToPriceFormat(totalNetHt.toString()),
                                alignment = TextAlignment.RIGHT
                            ),
                            )

                        segmentedLine(
                            LineSegment("Montant TVA:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(ticket.tIKMtTVA),
                                TextAlignment.RIGHT
                            ),
                        )

                        segmentedLine(
                            LineSegment("Timbre Fiscal:", TextAlignment.LEFT),
                            LineSegment(convertStringToPriceFormat(timbValue), TextAlignment.RIGHT),
                        )

                        /* segmentedLine(
                             LineSegment("Montant Apres Remise:", TextAlignment.LEFT),
                             LineSegment(
                                 convertStringToPriceFormat(
                                     (stringToDouble(ticket.tIKMtTTC) - stringToDouble(
                                         timbValue
                                     )).toString()
                                 ), TextAlignment.RIGHT
                             ),
                         )*/


                        var tikMttc = 0.0
                        var mntRedevance = 0.0

                        if ((client.cltMntRevImp?: 0.0) > 0.0) {
                            mntRedevance = stringToDouble(ticket.tIKMtTTC) - (stringToDouble(ticket.tIKMtTTC) / (1 + client.cltMntRevImp!! / 100))
                        }
                        if (facture != null) {
                            if (facture.factMntRevImp != null) {
                                if (stringToDouble(facture.factMntRevImp) > 0) {
                                    segmentedLine(
                                        LineSegment("Avance Imp:", TextAlignment.LEFT),
                                        LineSegment(
                                            convertStringToPriceFormat(facture.factMntRevImp),
                                            TextAlignment.RIGHT
                                        )
                                    )
                                }
                            }


                            tikMttc = Math.max(
                                stringToDouble(facture.factMntRevImp),
                                stringToDouble(ticket.tIKMtTTC)
                            )
                        } else tikMttc = stringToDouble(ticket.tIKMtTTC)

                        if (facture == null && client.cltMntRevImp!! > 0.0) {
                            mntRedevance = tikMttc - (tikMttc / (1 + client.cltMntRevImp!! / 100))

                            segmentedLine(
                                LineSegment("Redevance:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(mntRedevance.toString()),
                                    TextAlignment.RIGHT
                                )
                            )
                        }

                        segmentedLine(
                            LineSegment("Net A Payer:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(netTTC.toString()),
                                TextAlignment.RIGHT
                            )
                        )

                        if (reglementTicketPart != null) {
                            if (isCredit) {
                                printTicketPaymentDetails(
                                    mntCheque = reglementTicketPart.rEGCMntCheque ?: 0.0,
                                    mntEspece = reglementTicketPart.rEGCMntEspece ?: 0.0,
                                    mntTraite = reglementTicketPart.rEGCMntTraite ?: 0.0,
                                    mntReste = reglementTicketPart.made ?: 0.0,
                                )

                            }
                        } else if (reglement != null) {
                            printTicketPaymentDetails(
                                mntCheque = reglement.rEGCMntCheque ?: 0.0,
                                mntEspece = reglement.rEGCMntEspece ?: 0.0,
                                mntTraite = reglement.rEGCMntTraite ?: 0.0,
                                mntReste = reglement.made ?: 0.0,
                            )
                        }


                        textAlign(TextAlignment.LEFT)

                        printFooter(
                            context = context,
                            client = client,
                            horizontalSeparator = HORIZONTAL_SEPERATOR,
                            printParams = printParams
                        )





                        onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)


                        if (isCredit && reglementTicketPart != null) {
                            printReglement(
                                context = context,
                                reglementCaisse = ReglementCaisseWithTicketAndClient(
                                    reglementCaisse = reglementTicketPart,
                                    ticket = ticket,
                                    client = client
                                ),
                                utilisateur = utilisateur,
                                printParams = printParams,
                                prefixList = prefixList
                            )
                        }


                    }
                }
                .onLeft { err ->
                    onPrintResultChange(err.toString())
                }


        }


    }


    fun printRecapTicket(
        context: Context,
        ticket: Ticket,
        listLigneTicket: List<LigneTicketWithArticle>,
        client: Client,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        viewModelScope.launch {
            val charPerLine = printParams.paperSize
            val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)
            val btManager = BluetoothPrinterManager(context)


            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected

            btManager.openConnection(device)
                .onRight { connection ->
                    val config = PrinterConfiguration(charactersPerLine = charPerLine)
                    connection.print(config) {
                        charset(Charset.Windows1256)

                        textAlign(TextAlignment.CENTER)






                        bold(true)
                        line("Recapitulatif des Tickets")
                        bold(false)
                        line(ticket.tIKIdSCaisse)

                        line(stringPlural(
                            nbr = stringToDouble(ticket.nbreTicketsRecap).toInt(),
                            single = context.getString(R.string.ticket_title),
                            plural = context.getString(R.string.tickets)
                        ))

                        //  if (ticket.tIKEtat== TicketState.CREDIT.name && ticket.)
                        //HERE REG PARTIEL
                        line("Date: " + ticket.tIKDateHeureTicket.substringBefore("."))


                        printLogo(printParams = printParams)

                        printEtablismentInfo(charPerLine = charPerLine, printingData = printParams)



                        printClientInfo(
                            charPerLine = charPerLine,
                            idSCaisse = ticket.tIKIdSCaisse,
                            utilisateur = utilisateur
                        )
                        // MULTIPLE TEXT ALIGNMENTS PER LINE


                        bold(true)

                        if(printParams.taxeArticle) {
                            segmentedLine(
                                LineSegment("QTE", TextAlignment.LEFT),
                                LineSegment("P.U", TextAlignment.CENTER),
                                LineSegment("TOTAL HT", TextAlignment.CENTER),
                                LineSegment("TOTAL", TextAlignment.RIGHT),
                            )
                        } else {
                            segmentedLine(
                                LineSegment("QTE", TextAlignment.LEFT),
                                LineSegment("P.U", TextAlignment.CENTER),
                                LineSegment("TOTAL", TextAlignment.RIGHT),
                            )
                        }

                        // line(TABLE_HEADER_FOUR)
                        line(HORIZONTAL_SEPERATOR)
                        bold(false)
                        textAlign(TextAlignment.LEFT)

                        printTicketArticleTable(
                            ligneTicketWithArticleList = listLigneTicket,
                            printParams = printParams
                        )

                        bold(true)
                        line(HORIZONTAL_SEPERATOR)
                        segmentedLine(
                            LineSegment("Total Articles:", TextAlignment.LEFT),
                            LineSegment(listLigneTicket.size.toString(), TextAlignment.RIGHT),
                        )
                        withBold(false) {
                            line(HORIZONTAL_SEPERATOR)
                        }

                        val total = stringToDouble(ticket.tIKMtTTC) + stringToDouble(ticket.tIKMtRemise)


                        segmentedLine(
                            LineSegment("Montant Total:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(total.toString()),
                                TextAlignment.RIGHT
                            ),
                        )


                        segmentedLine(
                            LineSegment("Total Remise:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(ticket.tIKMtRemise),
                                TextAlignment.RIGHT
                            ),
                        )






                        segmentedLine(
                            LineSegment("Net A Payer:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(ticket.tIKMtTTC),
                                TextAlignment.RIGHT
                            ),
                        )

                        if (ticket.tIKMtCredit > 0)
                            segmentedLine(
                                LineSegment("Credit:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(ticket.tIKMtCredit.toString()),
                                    TextAlignment.RIGHT
                                ),
                            )

                        if (stringToDouble(ticket.tIKMtEspece) > 0)
                            segmentedLine(
                                LineSegment("Espece:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(ticket.tIKMtEspece),
                                    TextAlignment.RIGHT
                                ),
                            )

                        if (stringToDouble(ticket.tIKMtCheque) > 0)
                            segmentedLine(
                                LineSegment("Cheque:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(ticket.tIKMtCheque),
                                    TextAlignment.RIGHT
                                ),
                            )

                        if (stringToDouble(ticket.tIKMtrecue) > 0)
                            segmentedLine(
                                LineSegment("Tikets Restaurant:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(ticket.tIKMtrecue),
                                    TextAlignment.RIGHT
                                ),
                            )
                        /*else if(reglement!= ReglementCaisse()) {

                        }*/


                        printFooter(
                            context = context,
                            client = client,
                            printSolClt = false,
                            horizontalSeparator = HORIZONTAL_SEPERATOR,
                            printParams = printParams
                        )





                        onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)


                    }
                }
                .onLeft { err ->
                    onPrintResultChange(err.toString())
                }


        }


    }

    private fun CommandBuilder.printTicketPaymentDetails(
        mntCheque: Double,
        mntEspece: Double,
        mntTraite: Double,
        mntReste: Double
    ) {
        textAlign(TextAlignment.CENTER)
        line("- - - -")
        textAlign(TextAlignment.LEFT)
        if (mntEspece > 0.0)
            segmentedLine(
                LineSegment("Espece:", TextAlignment.LEFT),
                LineSegment(
                    convertStringToPriceFormat(mntEspece.toString()),
                    TextAlignment.RIGHT
                ),
            )




        if (mntCheque > 0.0)
            segmentedLine(
                LineSegment("Cheque:", TextAlignment.LEFT),
                LineSegment(
                    convertStringToPriceFormat(mntCheque.toString()),
                    TextAlignment.RIGHT
                ),
            )




        if (mntTraite > 0.0)
            segmentedLine(
                LineSegment("Tikets Restaurant:", TextAlignment.LEFT),
                LineSegment(
                    convertStringToPriceFormat(mntTraite.toString()),
                    TextAlignment.RIGHT
                ),
            )



        if (mntReste > 0.0)
            segmentedLine(
                LineSegment("A Rendre:", TextAlignment.LEFT),
                LineSegment(
                    convertStringToPriceFormat(mntReste.toString()),
                    TextAlignment.RIGHT
                ),
            )

    }

    /**
     *  * * * B O N ** L I V R A I S O N * * * *
     */
    fun printBonCommande(
        context: Context,
        bonCommandeWithClient: BonCommandeWithClient,
        lgBonCommandeWithArticle: List<LigneBonCommandeWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        viewModelScope.launch {
            val charPerLine = printParams.paperSize
            val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)

            val bonCommande = bonCommandeWithClient.bonCommande!!
            val client = bonCommandeWithClient.client!!
            val btManager = BluetoothPrinterManager(context)


            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected

            btManager.openConnection(device)
                .onRight { connection ->
                    val config = PrinterConfiguration(charactersPerLine = charPerLine)
                    connection.print(config) {
                        charset(Charset.Windows1256)

                        textAlign(TextAlignment.CENTER)



                        bold(true)
                        line("Bon Commande")
                        bold(false)
                        var num = bonCommande.dEVNum
                        if (num.contains("_")) {
                            val numTicket: List<String> = num.split("_")
                            num = "DEV_M_" + numTicket[2] + "_" + numTicket[3] + "_" + numTicket[5]
                        }
                        line("Num : $num")

                        //  if (ticket.tIKEtat== TicketState.CREDIT.name && ticket.)
                        //HERE REG PARTIEL
                        line("Date: " + bonCommande.dEVDate?.substringBefore("."))

                        printLogo(printParams = printParams)

                        printEtablismentInfo(charPerLine = charPerLine, printingData = printParams)

                        printClientInfo(
                            charPerLine = charPerLine,
                            client = client,
                            tIKNomClient = bonCommande.dEVClientName ?: "N/A",
                            tIKCodClt = bonCommande.dEVCodeClient ?: ""
                        )

                        printClientInfo(
                            charPerLine = charPerLine,
                            idSCaisse = "",
                            utilisateur = utilisateur
                        )
                        // MULTIPLE TEXT ALIGNMENTS PER LINE


                        bold(true)
                        if(printParams.taxeArticle) {
                            segmentedLine(
                                LineSegment("QTE", TextAlignment.LEFT),
                                LineSegment("P.U", TextAlignment.CENTER),
                                LineSegment("TOTAL HT", TextAlignment.CENTER),
                                LineSegment("TOTAL", TextAlignment.RIGHT),
                            )
                        }
                        else {
                            segmentedLine(
                                LineSegment("QTE", TextAlignment.LEFT),
                                LineSegment("P.U", TextAlignment.CENTER),
                                LineSegment("TOTAL", TextAlignment.RIGHT),
                            )
                        }
                        // line(TABLE_HEADER_FOUR)
                        line(HORIZONTAL_SEPERATOR)
                        bold(false)
                        textAlign(TextAlignment.LEFT)

                        printBonCommandeArticleTable(
                            listLgBonCommande = lgBonCommandeWithArticle,
                            printParams = printParams
                        )

                        bold(true)
                        line(HORIZONTAL_SEPERATOR)
                        segmentedLine(
                            LineSegment("Total Articles:", TextAlignment.LEFT),
                            LineSegment(
                                lgBonCommandeWithArticle.size.toString(),
                                TextAlignment.RIGHT
                            ),
                        )
                        withBold(false) {
                            line(HORIZONTAL_SEPERATOR)
                        }


                        if (stringToDouble(bonCommande.dEVMntTva) > 0) {
                            segmentedLine(
                                LineSegment("Montant TVA:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(bonCommande.dEVMntTva),
                                    TextAlignment.RIGHT
                                ),
                            )
                        }


                        segmentedLine(
                            LineSegment("Montant Total:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(bonCommande.dEVMntTTC),
                                TextAlignment.RIGHT
                            ),
                        )


                        if (stringToDouble(bonCommande.dEVRemise) > 0) {
                            segmentedLine(
                                LineSegment("Total Remise:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(bonCommande.dEVRemise),
                                    TextAlignment.RIGHT
                                ),
                            )
                        }

                        segmentedLine(
                            LineSegment("Net A Payer:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(
                                    (stringToDouble(bonCommande.dEVMntTTC) - stringToDouble(
                                        bonCommande.dEVRemise
                                    )).toString()
                                ),
                                TextAlignment.RIGHT
                            ),
                        )



                        printFooter(
                            context = context,
                            client = client,
                            horizontalSeparator = HORIZONTAL_SEPERATOR,
                            printParams = printParams
                        )




                        onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)

                    }
                }
                .onLeft { err ->
                    onPrintResultChange(err.toString())
                }


        }


    }

    private fun CommandBuilder.printBonCommandeArticleTable(
        listLgBonCommande: List<LigneBonCommandeWithArticle>,
        printParams: PrintingData
    ) {
        for (ligneBonCommandeWithArticle in listLgBonCommande) {
            val ligneBonCommande = ligneBonCommandeWithArticle.ligneBonCommande
            val qte: String = removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneBonCommande?.lGDEVQte))
            val label: String = ligneBonCommandeWithArticle.article?.aRTDesignation?: ligneBonCommande?.lGDEVCodeArt?: "N/A"

            val tauxRemise = removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneBonCommande?.lGDEVRemise))
            bold(true)
            line(label + if (stringToDouble(tauxRemise) != 0.0) " (-$tauxRemise %) " else "")
            bold(false)

            val mntHT = removeTrailingZeroInDouble(convertStringToDoubleFormat(calculateAmountExcludingTax(stringToDouble(ligneBonCommande?.lGDEVMntTTC), stringToDouble(ligneBonCommande?.lGDEVTva)).toString()))

            if(printParams.taxeArticle) {
                segmentedLine(
                    LineSegment(qte, TextAlignment.LEFT),
                    LineSegment(
                        removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneBonCommande?.lGDEVPUTTC)),
                        TextAlignment.CENTER
                    ),
                    LineSegment(mntHT, TextAlignment.CENTER),
                    LineSegment(
                        removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneBonCommande?.lGDEVMntTTC)),
                        TextAlignment.RIGHT
                    ),
                )
            }
            else {
                segmentedLine(
                    LineSegment(qte, TextAlignment.LEFT),
                    LineSegment(
                        removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneBonCommande?.lGDEVPUTTC)),
                        TextAlignment.CENTER
                    ),
                    LineSegment(
                        removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneBonCommande?.lGDEVMntTTC)),
                        TextAlignment.RIGHT
                    ),
                )
            }
            /* line(
                 printFourData(
                     qte,
                     StringUtils.priceFormat(ligneTicket.getlTPrixVente()),
                     (ligneTicket.getlTTauxRemise() as Double).toString(),
                     StringUtils.priceFormat(ligneTicket.getlTMtTTC())
                 ), 0, 0, false, false, 0
             )
             line(String.format(HORIZONTAL_SEPERATOR), 0, 1, false, false, 0)*/
        }
    }

    /**
     *  * * * B O N ** R E T O U R * * * *
     */
    fun printBonRetour(
        context: Context,
        bonRetourWithClient: BonRetourWithClient,
        lgBonRetourWithArticle: List<LigneBonRetourWithArticle>,
        utilisateur: Utilisateur,
        printParams: PrintingData
    ) {
        viewModelScope.launch {
            val charPerLine = printParams.paperSize
            val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)
            val SEMI_HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine/2)

            val btManager = BluetoothPrinterManager(context)

            val bonRetour = bonRetourWithClient.bonRetour?: BonRetour()
            val client = bonRetourWithClient.client?: Client()

            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected

            btManager.openConnection(device)
                .onRight { connection ->
                    val config = PrinterConfiguration(charactersPerLine = charPerLine)
                    connection.print(config) {
                        charset(Charset.Windows1256)

                        textAlign(TextAlignment.CENTER)



                        bold(true)
                        line("Bon Retour")
                        bold(false)
                        var num = bonRetour.bORNumero
                        /*  if (num.contains("_")) {
                              val numTicket: List<String> = num.split("_")
                              num = "DEV_M_" + numTicket[2] + "_" + numTicket[3] + "_" + numTicket[5]
                          }*/
                        line("Num : $num")

                        //  if (ticket.tIKEtat== TicketState.CREDIT.name && ticket.)
                        //HERE REG PARTIEL
                        line("Date: " + bonRetour.bORDate?.substringBefore("."))

                        printLogo(printParams = printParams)

                        printEtablismentInfo(charPerLine = charPerLine, printingData = printParams)

                        printClientInfo(
                            charPerLine = charPerLine,
                            client = client,
                            tIKNomClient = bonRetour.bORNomfrs ?: "N/A",
                            tIKCodClt = bonRetour.bORCodefrs ?: ""
                        )

                        printClientInfo(
                            charPerLine = charPerLine,
                            idSCaisse = "",
                            utilisateur = utilisateur
                        )
                        // MULTIPLE TEXT ALIGNMENTS PER LINE


                        bold(true)
                        segmentedLine(
                            LineSegment("QTE", TextAlignment.LEFT),
                            LineSegment("P.U", TextAlignment.CENTER),
                            LineSegment("TOTAL", TextAlignment.RIGHT),
                        )
                        // line(TABLE_HEADER_FOUR)
                        line(HORIZONTAL_SEPERATOR)
                        bold(false)
                        textAlign(TextAlignment.LEFT)

                        printBonRetourArticleTable(listLgBonRetour = lgBonRetourWithArticle)

                        bold(true)
                        textAlign(TextAlignment.CENTER)
                        line(SEMI_HORIZONTAL_SEPERATOR)
                        textAlign(TextAlignment.LEFT)
                        segmentedLine(
                            LineSegment("Total Articles:", TextAlignment.LEFT),
                            LineSegment(
                                lgBonRetourWithArticle.size.toString(),
                                TextAlignment.RIGHT
                            ),
                        )
                        withBold(false) {
                            textAlign(TextAlignment.CENTER)
                            line(SEMI_HORIZONTAL_SEPERATOR)
                            textAlign(TextAlignment.LEFT)
                        }


                      val mntTotal = convertStringToPriceFormat((abs(stringToDouble(bonRetour.bORMntTTC)) -  abs(stringToDouble(bonRetour.bORMntRemise))).toString())

                        segmentedLine(
                            LineSegment("Montant Total:", TextAlignment.LEFT),
                            LineSegment(
                                mntTotal,
                                TextAlignment.RIGHT
                            ),
                        )



                        printFooter(
                            context = context,
                            client = client,
                            horizontalSeparator = HORIZONTAL_SEPERATOR,
                            printParams = printParams
                        )



                        onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)

                    }
                }
                .onLeft { err ->
                    onPrintResultChange(err.toString())
                }


        }


    }

    private fun CommandBuilder.printBonRetourArticleTable(
        listLgBonRetour: List<LigneBonRetourWithArticle>
    ) {
        for (ligneBonRetourWithArticle in listLgBonRetour) {
            val ligneBonRetour = ligneBonRetourWithArticle.ligneBonRetour
            val article = ligneBonRetourWithArticle.article
            val qte: String = removeTrailingZeroInDouble(ligneBonRetour?.lIGBonEntreeQte?: "0")
            val label: String = article?.aRTDesignation?: "N/A"

            val tauxRemise = removeTrailingZeroInDouble(ligneBonRetour?.lIGBonEntreeRemise?: "0")
            bold(true)
            line(label + if (stringToDouble(tauxRemise) != 0.0) " (Remise: $tauxRemise %)" else "")
            bold(false)
            segmentedLine(
                LineSegment(qte, TextAlignment.LEFT),
                LineSegment(
                    article?.pvttc.toString(),
                    TextAlignment.CENTER
                ),
                LineSegment(
                    convertStringToDoubleFormat(ligneBonRetour?.lIGBonEntreeMntTTC?.removePrefix("-")),
                    TextAlignment.RIGHT
                ),
            )
            /* line(
                 printFourData(
                     qte,
                     StringUtils.priceFormat(ligneTicket.getlTPrixVente()),
                     (ligneTicket.getlTTauxRemise() as Double).toString(),
                     StringUtils.priceFormat(ligneTicket.getlTMtTTC())
                 ), 0, 0, false, false, 0
             )
             line(String.format(HORIZONTAL_SEPERATOR), 0, 1, false, false, 0)*/
        }
    }

    /**
     *  * * * R E G L M E N T * * * *
     */
    fun printReglement(
        context: Context,
        reglementCaisse: ReglementCaisseWithTicketAndClient,
        utilisateur: Utilisateur,
        printParams: PrintingData,
        prefixList: List<Prefixe>
    ) {
        viewModelScope.launch {
            val cliCode = reglementCaisse.reglementCaisse?.rEGCCodeClient ?: ""
            val client = reglementCaisse.client ?: Client(cLICode = cliCode, cLINomPren = cliCode)
            val prefixeBl = prefixList.firstOrNull { it.pREIdTable == "Bon_livraison" }?.pREPrefixe ?: "BL_M_"

            val charPerLine = printParams.paperSize
            val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)
            val ticket = reglementCaisse.ticket
            val regCaisse = reglementCaisse.reglementCaisse
            var isRegpartiel = false
            if ((regCaisse!!.rEGNumTicketPart?.toLongOrNull() ?: 0L) != 0L) isRegpartiel = true
            val btManager = BluetoothPrinterManager(context)


            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected

            btManager.openConnection(device)
                .onRight { connection ->
                    val config = PrinterConfiguration(charactersPerLine = charPerLine)
                    connection.print(config) {
                        charset(Charset.Windows1256)

                        textAlign(TextAlignment.CENTER)



                        bold(true)
                        line("Reglement")
                        bold(false)

                        var numTicket: String = getReglementNumber(
                            reglementCaisse = regCaisse,
                            ticket = ticket,
                            context = context,
                            prefixList = prefixList
                        )


                        line(numTicket)

                        //  if (ticket.tIKEtat== TicketState.CREDIT.name && ticket.)
                        //HERE REG PARTIEL
                        line("Date: " + regCaisse.rEGCDateReg?.substringBefore("."))

                        printLogo(printParams = printParams)

                        printEtablismentInfo(charPerLine = charPerLine, printingData = printParams)

                        printClientInfo(
                            charPerLine = charPerLine,
                            client = client,
                            tIKNomClient = regCaisse.rEGCNomPrenom ?: "N/A",
                            tIKCodClt = regCaisse.rEGCCodeClient ?: ""
                        )

                        printClientInfo(
                            charPerLine = charPerLine,
                            idSCaisse = regCaisse.rEGCIdCaisse ?: "N/A",
                            utilisateur = utilisateur
                        )
                        // MULTIPLE TEXT ALIGNMENTS PER LINE


                        bold(true)

                        if (abs(regCaisse.rEGCMntEspece!!) > 0) {
                            segmentedLine(
                                LineSegment("Espece:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(regCaisse.rEGCMntEspece.toString()),
                                    TextAlignment.RIGHT
                                ),
                            )
                        }

                        if (abs(regCaisse.rEGCMntCheque!!) > 0) {
                            segmentedLine(
                                LineSegment("Cheque:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(regCaisse.rEGCMntCheque.toString()),
                                    TextAlignment.RIGHT
                                ),
                            )
                        }


                        if (abs(regCaisse.rEGCMntTraite!!) > 0) {
                            segmentedLine(
                                LineSegment("Tikets Restaurant:", TextAlignment.LEFT),
                                LineSegment(
                                    convertStringToPriceFormat(regCaisse.rEGCMntTraite.toString()),
                                    TextAlignment.RIGHT
                                ),
                            )
                        }

                        val total: Double = regCaisse.rEGCMntEspece + regCaisse.rEGCMntCheque + regCaisse.rEGCMntTraite

                        textAlign(TextAlignment.CENTER)
                        line("- - - -")
                        textAlign(TextAlignment.LEFT)
                        segmentedLine(
                            LineSegment("Total:", TextAlignment.LEFT),
                            LineSegment(
                              //  convertStringToPriceFormat(regCaisse.rEGCMontant.toString()),
                                convertStringToPriceFormat(total.toString()),
                                TextAlignment.RIGHT
                            ),
                        )
                        printFooter(
                            context = context,
                            client = client,
                            horizontalSeparator = HORIZONTAL_SEPERATOR,
                            printParams = printParams
                        )



                        onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)

                    }
                }
                .onLeft { err ->
                    onPrintResultChange(err.toString())
                }


        }


    }

    private fun CommandBuilder.printEtablismentInfo(charPerLine: Int, printingData: PrintingData) {

        val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)




        if (!printingData.printCompanyName) return

        if (etablisement.desgEt!!.length > 16) {
            val stationDesignations: List<String> = etablisement.desgEt!!.split(" ")
            var stationDesignation = StringBuilder()
            val finalList: MutableList<String> = ArrayList()
            for (i in stationDesignations.indices) {
                if (stationDesignation.length + stationDesignations[i].length <= 16 && i != stationDesignations.size - 1) {
                    stationDesignation.append(stationDesignations[i]).append(" ")
                } else if (stationDesignation.length + stationDesignations[i].length <= 16 && i == stationDesignations.size - 1) {
                    finalList.add(stationDesignation.toString())
                    stationDesignation = StringBuilder()
                    stationDesignation.append(stationDesignations[i]).append(" ")
                    finalList.add(stationDesignation.toString())
                } else if (stationDesignation.length + stationDesignations[i].length > 16 && i == stationDesignations.size - 1) {
                    finalList.add(stationDesignation.toString())
                    stationDesignation = StringBuilder()
                    stationDesignation.append(stationDesignations[i]).append(" ")
                    finalList.add(stationDesignation.toString())
                } else {
                    finalList.add(stationDesignation.toString())
                    stationDesignation = StringBuilder()
                    stationDesignation.append(stationDesignations[i]).append(" ")
                }
            }
            for (designation in finalList) {
                withTextSize(width = 2, height = 2) {
                    withBold(enabled = true) {
                        line(designation)
                    }
                }
            }
        } else {
            withTextSize(width = 2, height = 2) {
                withBold(enabled = true) {
                    line(etablisement.desgEt!!)
                }
            }

        }




        line("    ")

        val etabAdresse = etablisement.etAdresse ?: "N/A"

        if (etabAdresse.length > 24) {
            textAlign(TextAlignment.LEFT)
            line("Adresse: $etabAdresse")
            textAlign(TextAlignment.CENTER)
        } else
            segmentedLine(
                LineSegment("Adresse:", TextAlignment.LEFT),
                LineSegment(etablisement.etAdresse ?: "N/A", TextAlignment.RIGHT),
            )
        segmentedLine(
            LineSegment("Tel:", TextAlignment.LEFT),
            LineSegment(etablisement.etTel1 ?: etablisement.etTel2 ?: "N/A", TextAlignment.RIGHT),
        )

        segmentedLine(
            LineSegment("MF:", TextAlignment.LEFT),
            LineSegment(etablisement.etMAtriculeF ?: "N/A", TextAlignment.RIGHT),
        )
        bold(true)
        line(HORIZONTAL_SEPERATOR)
        bold(false)
    }

    private fun CommandBuilder.printClientInfo(
        charPerLine: Int,
        client: Client,
        tIKNomClient: String,
        tIKCodClt: String,
        clientAdress: String = ""
    ) {
        val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)

        if (tIKNomClient.length > 24) {
            textAlign(TextAlignment.LEFT)
            line("Client: $tIKNomClient")
            textAlign(TextAlignment.CENTER)
        } else
            segmentedLine(
                LineSegment("Client:", TextAlignment.LEFT),
                LineSegment(tIKNomClient, TextAlignment.RIGHT)
            )

        segmentedLine(
            LineSegment("Code Client:", TextAlignment.LEFT),
            LineSegment(tIKCodClt, TextAlignment.RIGHT),
        )

        segmentedLine(
            LineSegment("MF Client:", TextAlignment.LEFT),
            LineSegment(client.cLIMatFisc ?: "N/A", TextAlignment.RIGHT),
        )

        segmentedLine(
            LineSegment("Tel Client:", TextAlignment.LEFT),
            LineSegment(client.cLITel1 ?: client.cLITel2 ?: "N/A", TextAlignment.RIGHT),
        )

        val ctlAdresse = client.cLIAdresse ?: clientAdress
        if (ctlAdresse.length > 24) {
            textAlign(TextAlignment.LEFT)
            line("Adresse Client: $ctlAdresse")
            textAlign(TextAlignment.CENTER)
        } else
            segmentedLine(
                LineSegment("Adresse Client:", TextAlignment.LEFT),
                LineSegment(ctlAdresse, TextAlignment.RIGHT),
            )

        bold(true)
        line(HORIZONTAL_SEPERATOR)
        bold(false)
    }

    private fun CommandBuilder.printClientInfo(
        charPerLine: Int,
        idSCaisse: String,
        utilisateur: Utilisateur
    ) {
        val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)

        if (idSCaisse.isNotEmpty())
            segmentedLine(
                LineSegment("Caisse:", TextAlignment.LEFT),
                LineSegment(idSCaisse, TextAlignment.RIGHT),
            )

        segmentedLine(
            LineSegment("Operateur:", TextAlignment.LEFT),
            LineSegment(utilisateur.Nom + " " + utilisateur.Prenom, TextAlignment.RIGHT),
        )

        bold(true)
        line(HORIZONTAL_SEPERATOR)
        bold(false)
    }


    private fun CommandBuilder.printTicketArticleTable(
        ligneTicketWithArticleList: List<LigneTicketWithArticle>,
        printParams: PrintingData
    ) {
        for (ligneTicketWithArticle in ligneTicketWithArticleList) {
            val ligneTicket = ligneTicketWithArticle.ligneTicket
            val article = ligneTicketWithArticle.article
            val qte: String = removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneTicket?.lTQte))
            val label: String = article?.aRTDesignation?: ligneTicket?.lTCodArt?: "N/A"
            val tauxRemise = removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneTicket?.lTTauxRemise))


            val mntHT = removeTrailingZeroInDouble(convertStringToDoubleFormat(calculateAmountExcludingTax(stringToDouble(ligneTicket?.lTMtTTC), stringToDouble(ligneTicket?.lTTVA)).toString()))
            bold(true)
            line(label + if (stringToDouble(tauxRemise) != 0.0) " (-$tauxRemise %) " else "")
            bold(false)
            if(printParams.taxeArticle) {
                segmentedLine(
                    LineSegment(qte, TextAlignment.LEFT),
                    LineSegment(
                        removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneTicket?.lTPrixVente)),
                        TextAlignment.CENTER
                    ),
                   LineSegment(mntHT, TextAlignment.CENTER),
                    LineSegment(
                        removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneTicket?.lTMtTTC)),
                        TextAlignment.RIGHT
                    ),
                )
            }
            else {
                segmentedLine(
                    LineSegment(qte, TextAlignment.LEFT),
                    LineSegment(
                        removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneTicket?.lTPrixVente)),
                        TextAlignment.CENTER
                    ),
                    LineSegment(
                        removeTrailingZeroInDouble(convertStringToDoubleFormat(ligneTicket?.lTMtTTC)),
                        TextAlignment.RIGHT
                    ),
                )
            }
            /* line(
                 printFourData(
                     qte,
                     StringUtils.priceFormat(ligneTicket.getlTPrixVente()),
                     (ligneTicket.getlTTauxRemise() as Double).toString(),
                     StringUtils.priceFormat(ligneTicket.getlTMtTTC())
                 ), 0, 0, false, false, 0
             )
             line(String.format(HORIZONTAL_SEPERATOR), 0, 1, false, false, 0)*/
        }
    }


    /**
     ****************** I N V E N T O R Y ************************
     */

    fun printInventaire(
        context: Context,
        lgInventaire: List<LigneInventaire>,
        inventaire: Inventaire,
        articleMapByBarCode: Map<String, Article>,
        station: Station,
        printParams: PrintingData
    ) {

        viewModelScope.launch {
            val charPerLine = printParams.paperSize
            val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)
            val btManager = BluetoothPrinterManager(context)


            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected


            btManager.openConnection(device).onRight { connection ->
                val config = PrinterConfiguration(charactersPerLine = charPerLine)
                connection.print(config) {
                    charset(Charset.Windows1256)

                    textAlign(TextAlignment.CENTER)


                    val num = "Num : " + inventaire.iNVCode




                    bold(true)
                    withTextSize(width = 2, height = 3) {
                        line("Inventaire")
                    }

                  if(!inventaire.iNVEtat.isNullOrBlank()) {
                      withTextSize(width = 2, height = 1) {
                          line(inventaire.iNVEtat?: "")
                      }
                  }

                    bold(false)
                    line(num)



                    line("Date: " + inventaire.iNVDateFormatted)

                    printLogo(printParams = printParams)

                    segmentedLine(
                        LineSegment("Station", TextAlignment.LEFT),
                        LineSegment(station.sTATDesg, TextAlignment.RIGHT),
                    )

                    // MULTIPLE TEXT ALIGNMENTS PER LINE
                    withBold(true) {
                        line(HORIZONTAL_SEPERATOR)
                    }

                    bold(true)
                    segmentedLine(
                        LineSegment("DESIGNATION", TextAlignment.LEFT),
                        LineSegment("Qte Stations", TextAlignment.CENTER),
                        LineSegment("Qte", TextAlignment.RIGHT),
                    )
                    // line(TABLE_HEADER_FOUR)
                    line(HORIZONTAL_SEPERATOR)
                    bold(false)
                    textAlign(TextAlignment.LEFT)

                    printInventaireArticleTable(
                        listLigneInventaire = lgInventaire,
                        articleMapByBarCode = articleMapByBarCode
                    )
                    bold(true)
                    line(HORIZONTAL_SEPERATOR)
                    segmentedLine(
                        LineSegment("Total Articles:", TextAlignment.LEFT),
                        LineSegment(lgInventaire.size.toString(), TextAlignment.RIGHT),
                    )



                    line(HORIZONTAL_SEPERATOR)
                    bold(false)



                    printAppVersion(printParams = printParams)
                    line("         ")
                    line("         ")
                    line("         ")


                }

                onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)
            }
                .onLeft { err ->
                    Log.d("oplsdtgg", " 1  " + err.toString())
                    onPrintResultChange(err.toString())
                }


        }


    }

    private fun CommandBuilder.printInventaireArticleTable(
        listLigneInventaire: List<LigneInventaire>,
        articleMapByBarCode: Map<String, Article>
    ) {
        for (ligneInventaire in listLigneInventaire) {
            val art = articleMapByBarCode[ligneInventaire.lGINVCodeArticle]
                ?: Article(aRTCodeBar = ligneInventaire.lGINVCodeArticle)
            val qteStation: String = stringToDouble(ligneInventaire.lGINVQteReel).toString()
            val qte: String = stringToDouble(ligneInventaire.lGINVQteStock).toString()
            val label: String = art.aRTDesignation.ifEmpty { "N/A" }
            val codeArt: String = art.aRTCodeBar


            segmentedLine(
                LineSegment("$label ($codeArt)", TextAlignment.LEFT),
                LineSegment(qte, TextAlignment.CENTER),
                LineSegment(qteStation, TextAlignment.RIGHT),
            )
            /* line(
                 printFourData(
                     qte,
                     StringUtils.priceFormat(ligneTicket.getlTPrixVente()),
                     (ligneTicket.getlTTauxRemise() as Double).toString(),
                     StringUtils.priceFormat(ligneTicket.getlTMtTTC())
                 ), 0, 0, false, false, 0
             )
             line(String.format(HORIZONTAL_SEPERATOR), 0, 1, false, false, 0)*/
        }
    }


    /***
     * **********     B O N ** T R A N S F E R T *****************
     */
    fun printBonTransfert(
        context: Context,
        lgBonTransfert: List<LigneBonLivraison>,
        bonTransfert: BonLivraison,
        articleMapByBarCode: Map<String, Article>,
        stationSource: Station,
        stationDestination: Station,
        printParams: PrintingData
    ) {


        viewModelScope.launch {
            val charPerLine = printParams.paperSize
            val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)
            val btManager = BluetoothPrinterManager(context)


            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected


            btManager.openConnection(device)
                .onRight { connection ->
                    val config = PrinterConfiguration(charactersPerLine = charPerLine)
                    connection.print(config) {
                        charset(Charset.Windows1256)

                        textAlign(TextAlignment.CENTER)


                        val num = "Num : " + bonTransfert.bONTransNum




                        bold(true)
                        withTextSize(width = 2, height = 3) {
                            line("Bon Transfert")
                        }

                        if(!bonTransfert.bONTransEtat.isNullOrBlank()) {
                            withTextSize(width = 2, height = 1) {
                                line(bonTransfert.bONTransEtat?.replace("é","")?:"") // .replace("é","") because we cant print letter é (maybe play with encoding)
                            }
                        }
                        bold(false)
                        line(num)



                        line("Date: " + bonTransfert.bONTransDateFormatted)

                        printLogo(printParams = printParams)

                        segmentedLine(
                            LineSegment("Station Source", TextAlignment.LEFT),
                            LineSegment(stationSource.sTATDesg, TextAlignment.RIGHT),
                        )

                        segmentedLine(
                            LineSegment("Station Destination", TextAlignment.LEFT),
                            LineSegment(stationDestination.sTATDesg, TextAlignment.RIGHT),
                        )

                        // MULTIPLE TEXT ALIGNMENTS PER LINE
                        withBold(true) {
                            line(HORIZONTAL_SEPERATOR)
                        }

                        bold(true)
                        segmentedLine(
                            LineSegment("DESIGNATION", TextAlignment.LEFT),
                            LineSegment("Qte", TextAlignment.CENTER),
                            LineSegment("Prix", TextAlignment.RIGHT),
                        )
                        // line(TABLE_HEADER_FOUR)
                        line(HORIZONTAL_SEPERATOR)
                        bold(false)
                        textAlign(TextAlignment.LEFT)

                        printBonTransfertArticleTable(
                            listLigneBonTranfert = lgBonTransfert,
                            articleMapByBarCode = articleMapByBarCode
                        )
                        bold(true)
                        line(HORIZONTAL_SEPERATOR)
                        segmentedLine(
                            LineSegment("Total Articles:", TextAlignment.LEFT),
                            LineSegment(lgBonTransfert.size.toString(), TextAlignment.RIGHT),
                        )



                        line(HORIZONTAL_SEPERATOR)
                        bold(false)



                        printAppVersion(printParams = printParams)
                        line("         ")
                        line("         ")
                        line("         ")

                        onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)
                    }

                }
                .onLeft { err ->
                    onPrintResultChange(err.toString())
                }


        }


    }


    private fun CommandBuilder.printBonTransfertArticleTable(
        listLigneBonTranfert: List<LigneBonLivraison>,
        articleMapByBarCode: Map<String, Article>
    ) {
        for (ligneInventaire in listLigneBonTranfert) {
            val art = articleMapByBarCode[ligneInventaire.lGBonTransCodeArt]
                ?: Article(aRTCodeBar = ligneInventaire.lGBonTransCodeArt)
            val qte: String = ligneInventaire.qteDecTransferer ?: "N/A"
            val prix: String = ligneInventaire.lGBonTransPACHATNHT ?: "N/A"
            val label: String = art.aRTDesignation.ifEmpty { "N/A" }
            val codeArt: String = art.aRTCodeBar


            val finalPrice =
                if (ligneInventaire.qteTransfert != 1.0) {
                    removeTrailingZeroInDouble(
                        CalculationsUtils.totalPriceArticle(
                            price = prix,
                            quantity = ligneInventaire.qteTransfert.toString()
                        ) + " (" + removeTrailingZeroInDouble(prix) + ")"
                    )
                } else removeTrailingZeroInDouble(prix)
            segmentedLine(
                LineSegment("$label ($codeArt)", TextAlignment.LEFT),
                LineSegment(qte, TextAlignment.CENTER),
                // LineSegment(convertStringToPriceFormat(prix), TextAlignment.RIGHT),
                LineSegment(finalPrice, TextAlignment.RIGHT),
            )
            /* line(
                 printFourData(
                     qte,
                     StringUtils.priceFormat(ligneTicket.getlTPrixVente()),
                     (ligneTicket.getlTTauxRemise() as Double).toString(),
                     StringUtils.priceFormat(ligneTicket.getlTMtTTC())
                 ), 0, 0, false, false, 0
             )
             line(String.format(HORIZONTAL_SEPERATOR), 0, 1, false, false, 0)*/
        }
    }


    /***
     * **********     B O N ** A C H A T *****************
     */
    fun printBonAchat(
        context: Context,
        lgBonEntree: List<LigneBonEntree>,
        bonEntree: BonEntree,
        articleMapByBarCode: Map<String, Article>,
        station: Station,
        fournisseur: Fournisseur,
        printParams: PrintingData
    ) {


        viewModelScope.launch {

            val charPerLine = printParams.paperSize
            val HORIZONTAL_SEPERATOR = setHorizontalSeperator(charsPerLines = charPerLine)
            val btManager = BluetoothPrinterManager(context)


            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected


            btManager.openConnection(device)
                .onRight { connection ->
                    val config = PrinterConfiguration(charactersPerLine = charPerLine)
                    connection.print(config) {
                        charset(Charset.Windows1256)

                        textAlign(TextAlignment.CENTER)


                        val num = "Num: " + bonEntree.bONENTNum


                        val typePiece = bonEntree.bONENTTypePiece

                        bold(true)
                        withTextSize(width = 2, height = 3) {
                            line("Bon Achat")
                        }

                        if (typePiece == Globals.FACTURE)
                            withTextSize(width = 2, height = 2) {
                                line(typePiece)
                            }
                        bold(false)
                        line(num)



                        line("Date: " + bonEntree.bONENTDateFormatted)

                        printLogo(printParams = printParams)

                        segmentedLine(
                            LineSegment("Station", TextAlignment.LEFT),
                            LineSegment(station.sTATDesg, TextAlignment.RIGHT),
                        )

                        segmentedLine(
                            LineSegment("Fournisseur", TextAlignment.LEFT),
                            LineSegment(fournisseur.fRSNomf, TextAlignment.RIGHT),
                        )


                        segmentedLine(
                            LineSegment("Type piece", TextAlignment.LEFT),
                            LineSegment(bonEntree.bONENTType, TextAlignment.RIGHT),
                        )

                        segmentedLine(
                            LineSegment("Num piece", TextAlignment.LEFT),
                            LineSegment(bonEntree.bONENTNumPiece ?: "N/A", TextAlignment.RIGHT),
                        )
                        // MULTIPLE TEXT ALIGNMENTS PER LINE
                        withBold(true) {
                            line(HORIZONTAL_SEPERATOR)
                        }

                        bold(true)
                        segmentedLine(
                            LineSegment("DESIGNATION", TextAlignment.LEFT),
                            LineSegment("Qte", TextAlignment.CENTER),
                            LineSegment("Prix", TextAlignment.RIGHT),
                        )
                        // line(TABLE_HEADER_FOUR)
                        line(HORIZONTAL_SEPERATOR)
                        bold(false)
                        textAlign(TextAlignment.LEFT)

                        printBonAchatArticleTable(
                            listLigneBonAchat = lgBonEntree,
                            articleMapByBarCode = articleMapByBarCode
                        )
                        bold(true)
                        line(HORIZONTAL_SEPERATOR)
                        segmentedLine(
                            LineSegment("Total Articles:", TextAlignment.LEFT),
                            LineSegment(lgBonEntree.size.toString(), TextAlignment.RIGHT),
                        )
                        line(HORIZONTAL_SEPERATOR)
                        bold(false)
                        line("         ")
                        segmentedLine(
                            LineSegment("Montant HT:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(bonEntree.bONENTMntHT),
                                TextAlignment.RIGHT
                            ),
                        )
                        segmentedLine(
                            LineSegment("Montant Remise:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(bonEntree.bONENTRemise),
                                TextAlignment.RIGHT
                            ),
                        )

                        segmentedLine(
                            LineSegment("Montant TVA:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(bonEntree.bONENTMntTva),
                                TextAlignment.RIGHT
                            ),
                        )


                        if (typePiece == Globals.FACTURE)
                            segmentedLine(
                                LineSegment("Timbre Fiscal:", TextAlignment.LEFT),
                                LineSegment(convertStringToPriceFormat("1"), TextAlignment.RIGHT),
                            )



                        segmentedLine(
                            LineSegment("Montant TTC:", TextAlignment.LEFT),
                            LineSegment(
                                convertStringToPriceFormat(bonEntree.bONENTMntTTC),
                                TextAlignment.RIGHT
                            ),
                        )




                        printAppVersion(printParams = printParams)


                        line("         ")
                        line("         ")
                        line("         ")

                        onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)

                    }
                }
                .onLeft { err ->
                    onPrintResultChange(err.toString())
                }


        }


    }


    private fun CommandBuilder.printBonAchatArticleTable(
        listLigneBonAchat: List<LigneBonEntree>,
        articleMapByBarCode: Map<String, Article>
    ) {
        for (ligneBonAchat in listLigneBonAchat) {
            val art = articleMapByBarCode[ligneBonAchat.lIGBonEntreeCodeArt]
                ?: Article(aRTCodeBar = ligneBonAchat.lIGBonEntreeCodeArt)
            val qte: String = ligneBonAchat.lIGBonEntreeQte ?: "N/A"
            val prix: String = ligneBonAchat.lIGBonEntreeMntTTC ?: "N/A"
            val label: String = art.aRTDesignation.ifEmpty { "N/A" }
            val codeArt: String = art.aRTCodeBar


            segmentedLine(
                LineSegment("$label ($codeArt)", TextAlignment.LEFT),
                LineSegment(qte, TextAlignment.CENTER),
                LineSegment(convertStringToPriceFormat(prix), TextAlignment.RIGHT),
            )
            /* line(
                 printFourData(
                     qte,
                     StringUtils.priceFormat(ligneTicket.getlTPrixVente()),
                     (ligneTicket.getlTTauxRemise() as Double).toString(),
                     StringUtils.priceFormat(ligneTicket.getlTMtTTC())
                 ), 0, 0, false, false, 0
             )
             line(String.format(HORIZONTAL_SEPERATOR), 0, 1, false, false, 0)*/
        }
    }

    private fun CommandBuilder.printAppVersion(printParams: PrintingData) {

        val canPrintAppVersion = printParams.printAppVersion

        if (canPrintAppVersion) {
            line("         ")
            segmentedLine(LineSegment(MobileCodeGeneration.versionCode, TextAlignment.RIGHT))
        }


    }


    private fun CommandBuilder.printFooter(
        context: Context,
        client: Client,
        horizontalSeparator: String,
        printSolClt: Boolean = true,
        printParams: PrintingData
    ) {


        if (printParams.printCachet) {
            line(horizontalSeparator)

            if (printParams.paperSize == 48) {
                line("+--------------------+  +-------------------+ ")
                line("|                    |  |                   | ")
                line("|                    |  |                   | ")
                line("|                    |  |                   | ")
                line("+--------------------+  +-------------------+ ")
                line("  Cachet et signature     Cachet et signature ")
                line("      fournisseur               client ")
            } else {
                line("+--------------+ +-------------+")
                line("|              | |             |")
                line("|              | |             |")
                line("+--------------+ +-------------+")
                line("   Fournisseur       Client")

            }
        }
        bold(false)

        line("         ")







        if (printParams.printClientSold && printSolClt) {
            line(horizontalSeparator)
            line("         ")
            withBold(true) {
                segmentedLine(
                    LineSegment(
                        context.getString(com.asmtunis.procaisseinventory.R.string.sold_client),
                        TextAlignment.LEFT
                    ),
                    LineSegment(
                        convertStringToPriceFormat(client.solde),
                        TextAlignment.RIGHT
                    ),
                )
            }

          //  line("         ")
            line(horizontalSeparator)
        }




        line("         ")
        printAppVersion(printParams = printParams)

        line("         ")
        line("         ")

    }


    private fun CommandBuilder.printLogo(printParams: PrintingData) {

        line(" ")

        if (!printParams.printIcon) return

        if (logo.isEmpty()) return
        val decodedString: ByteArray = Base64.decode(logo)
        val bmp = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.size)

        bitmap(bitmapToBytes(bitmap = bmp, height = 100))
        //  bitmap(bitmapToBytes(bitmap = bmp, height = bmp.height))


        line(" ")

    }


    /***
     * **********     T I C K E T  *** R A Y O N  *****************
     */
    fun printTicketRayon(
        context: Context,
        article: Article,
        printParams: PrintingData
    ) {


        viewModelScope.launch {

            val btManager = BluetoothPrinterManager(context)


            //   val device = btManager.pairedPrinters().firstOrNull() ?: return MyError.NotFound
            val device = btManager.pairedPrinters().getOrElse { null }
                ?.firstOrNull { it.address == deviceAddress }
                ?: return@launch
            //   val connection = btManager.openConnection(bt).takeIf { it.isOpen } ?: return MynError.Disconnected


            btManager.openConnection(device)
                .onRight { connection ->
                    val charPerLine = printParams.paperSize
                    val config = PrinterConfiguration(charactersPerLine = charPerLine)
                    connection.print(config) {
                        charset(Charset.Windows1256)

                        textAlign(TextAlignment.CENTER)


                        val bmp = draw(article = article)


                        if (bmp == null) {
                            onPrintResultChange("Error Bitmap Null")
                            return@print
                        }

                        bitmap(bitmapToBytes(bitmap = bmp, height = 200))

                        line("         ")
                        line("         ")
                        line("         ")
                        onPrintResultChange(BluetoothStatus.PRINT_SUCCESS.status)

                    }
                }
                .onLeft { err ->
                    onPrintResultChange(err.toString())
                }


        }


    }

    private fun draw(article: Article): Bitmap? {
        val width = 1090
        val height = 400
        val bitmap = createBitmap(width, height)
        val mPaint = Paint()
        val canvas = Canvas(bitmap)
        val center_x = width / 2
        val center_y = height / 2
        canvas.drawColor(Color.LTGRAY)

        // prepare a paint
        mPaint.style = Paint.Style.STROKE
        mPaint.color = Color.BLACK
        mPaint.isAntiAlias = true
        mPaint.strokeWidth = 3F

        // draw a rectangle
        val rectangle = Rect(
            0,  // Left
            0,  // Top
            canvas.width,  // Right
            canvas.height // Bottom
        )
        canvas.drawRect(rectangle, mPaint)
        canvas.save()
        mPaint.textAlign = Paint.Align.CENTER
        mPaint.style = Paint.Style.FILL
        mPaint.setTypeface(Typeface.DEFAULT_BOLD)
        mPaint.textSize = 160F
        canvas.drawText(
            convertStringToDoubleFormat(article.prixSolde),
            center_x.toFloat(),
            (center_y - 80).toFloat(),
            mPaint
        )
        canvas.restore()
        canvas.save()
        mPaint.textAlign = Paint.Align.CENTER
        mPaint.style = Paint.Style.FILL
        mPaint.setTypeface(Typeface.DEFAULT_BOLD)
        mPaint.textSize = 60F
        canvas.drawText(article.aRTDesignation, center_x.toFloat(), center_y.toFloat(), mPaint)
        canvas.restore()
        canvas.save()
        mPaint.textAlign = Paint.Align.CENTER
        mPaint.style = Paint.Style.FILL
        mPaint.setTypeface(Typeface.DEFAULT_BOLD)
        mPaint.textSize = 60F
        canvas.drawText(
            "DT", canvas.width - mPaint.textSize,
            0 + mPaint.textSize + 10, mPaint
        )
        canvas.restore()
        canvas.save()
        mPaint.textAlign = Paint.Align.CENTER
        mPaint.style = Paint.Style.FILL
        mPaint.setTypeface(Typeface.DEFAULT_BOLD)
        mPaint.textSize = 40F
        canvas.rotate(-90f)
        val margin = 50f
        canvas.drawText(
            article.aRTCodeBar,
            -canvas.height / 2f,
            margin,
            mPaint
        )
        canvas.restore()
        canvas.save()
        val barcode: Bitmap = encodeAsBitmap(
            article.aRTCodeBar,
            BarcodeFormat.CODE_128,
            (canvas.width - margin * 2.3).toInt(),
            (canvas.height / 3.2f).toInt()
        ) ?: return null
        canvas.drawBitmap(
            barcode,
            (margin * 1.5).toFloat(),
            (canvas.height - 165).toFloat(),
            mPaint
        )

        canvas.restore()
        canvas.save()

        //  Application.database.ticketrayonDAO().delete(ticketRayon);
        return bitmap
    }


    private fun encodeAsBitmap(
        source: String?,
        format: BarcodeFormat?,
        width: Int,
        height: Int
    ): Bitmap? {
        val result: BitMatrix
        try {
            val hints: MutableMap<EncodeHintType, Any?> = EnumMap(
                EncodeHintType::class.java
            )
            hints[EncodeHintType.CHARACTER_SET] = source
            result = MultiFormatWriter().encode(source, format, width, height, hints)
        } catch (e: IllegalArgumentException) {
            return null
        } catch (e: WriterException) {
            return null
        }
        val w = result.width
        val h = result.height
        val pixels = IntArray(w * h)
        for (y in 0 until h) {
            val offset = y * w
            for (x in 0 until w) {
                pixels[offset + x] = if (result[x, y]) Color.BLACK else Color.TRANSPARENT
            }
        }
        val bitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888)
        bitmap.setPixels(pixels, 0, width, 0, 0, w, h)
        return bitmap
    }


}