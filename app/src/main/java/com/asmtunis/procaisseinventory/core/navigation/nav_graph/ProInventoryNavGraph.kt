package com.asmtunis.procaisseinventory.core.navigation.nav_graph

import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.navigation
import androidx.navigation.toRoute
import com.asmtunis.procaisseinventory.articles.consultation.screens.ArticlesScreen
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ArticleTextValidationViewModel
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticlesScreenNoCalcul
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.*
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_inventory.achat.screens.AchatDetailPane
import com.asmtunis.procaisseinventory.pro_inventory.achat.screens.AchatScreen
import com.asmtunis.procaisseinventory.pro_inventory.achat.screens.AddAchatScreen
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui.BonTranfertAddScreen
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui.BonTranfertDetailPane
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.ui.BonTransfertScreen
import com.asmtunis.procaisseinventory.pro_inventory.dashboard.InventoryHomeScreen
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.screens.InventaireAddScreen
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.screens.InventaireDetailPane
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.screens.InventaireScreen
import com.asmtunis.procaisseinventory.pro_inventory.sync.ProInventorySyncScreen
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens.TicketRayonScreen
import com.asmtunis.procaisseinventory.pro_inventory.ticket_rayon.screens.TicketRayonViewModel
import com.asmtunis.procaisseinventory.pro_inventory.update.ProInventoryUpdateDataScreen
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.view_model.*
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.view_model.InventairePrintViewModel
import com.asmtunis.procaisseinventory.pro_inventory.bon_transfert.view_model.BonTransfertPrintViewModel

@ExperimentalMaterial3Api
fun NavGraphBuilder.proInventoryNavGraph(
    navigate: (route: Any) -> Unit,
    navigatePopUpTo: (route: Any, popUpTo: Any, isInclusive: Boolean) -> Unit,
    popBackStack: () -> Unit,
    navigateUp: () -> Unit,
    dataViewModel: DataViewModel,
    settingVM: SettingViewModel,
    networkErrorsVM: NetworkErrorsViewModel,
    networkViewModel: NetworkViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    articlesViewModel : ArticlesViewModel,
    articleTxtValidViewModel : ArticleTextValidationViewModel,
    barCodeViewModel : BarCodeViewModel,

    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    mainViewModel : MainViewModel,
    ticketRayonViewModel : TicketRayonViewModel,
    selectArtInventoryVM: SelectArticleNoCalculViewModel,
    selectArtMobilityVM: SelectArticleCalculViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    sunmiPrintManager: SunmiPrintManager,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    proInventoryViewModels: ProInventoryViewModels
    ) {


    navigation<InventoryGraph>(
         startDestination = InventoryHomeRoute
    ) {
        composable<SelectArticlesNoCalculRoute> { arg->
            val args = arg.toRoute<SelectArticlesNoCalculRoute>()
            SelectArticlesScreenNoCalcul(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                stationOrigineCode = args.stationOrigineCode,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                articlesViewModel = articlesViewModel,
                barCodeViewModel = barCodeViewModel,
                mainViewModel = mainViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                selectArtNoCalculVM = selectArtInventoryVM,
                settingViewModel = settingVM
            )
        }

        composable<InventoryHomeRoute> {
            InventoryHomeScreen(
                navigate = { navigate(it) },
                networkViewModel = networkViewModel,
                networkErrorsVM = networkErrorsVM,
                dataViewModel = dataViewModel,
                navDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                syncInventoryViewModel = syncInventoryViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel= getSharedDataViewModel,
                mainViewModel = mainViewModel,
                settingViewModel = settingVM,
                syncProcaisseViewModels = syncProcaisseViewModels,
                syncSharedViewModels = syncSharedViewModels
            )
        }

        composable<InventoryAchatRoute> {
            AchatScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                barCodeViewModel =barCodeViewModel,
                navDrawerViewmodel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                achatViewModel = proInventoryViewModels.achatViewModel,
                mainViewModel = mainViewModel,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                selectArtCalculVM = selectArtMobilityVM,

                syncProcaisseViewModels = syncProcaisseViewModels,
                syncSharedViewModels = syncSharedViewModels,
                syncInventoryViewModel = syncInventoryViewModel,
                settingViewModel = settingVM
            )
        }

        composable<InventoryAchatDetailRoute> {
            AchatDetailPane(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                networkViewModel = networkViewModel,
                bluetoothVM = bluetoothVM,
                printViewModel = printViewModel,
                achatViewModel = proInventoryViewModels.achatViewModel,
                dataViewModel =dataViewModel,
                mainViewModel = mainViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                selectArtCalculVM = selectArtMobilityVM,
                settingViewModel = settingVM
            )
        }

        composable<InventoryAddAchatRoute> {
            AddAchatScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                networkViewModel = networkViewModel,
                barCodeViewModel = barCodeViewModel,
                achatViewModel = proInventoryViewModels.achatViewModel,
                dataViewModel =dataViewModel,
                mainViewModel = mainViewModel,

                navigationDrawerViewModel = navigationDrawerViewModel,
                selectArtCalculVM = selectArtMobilityVM,
                settingViewModel = settingVM
            )
        }

        composable<UpdateLocalDbInventoryRoute> {
            ProInventoryUpdateDataScreen(
                navigate = { navigate(it) },
                navigatePopUpTo = { route: String, popUpTo: String, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                popBackStack = { popBackStack() },
                navigateUp = { navigateUp() },
                networkViewModel = networkViewModel,
                dataViewModel =dataViewModel,
                navDrawerViewModel = navigationDrawerViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                settingViewModel = settingVM,
                mainViewModel = mainViewModel
            )
        }



        composable<InventoryInventaireRoute> {
            InventaireScreen(
                navigate = { navigate(it) },
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                barCodeViewModel = barCodeViewModel,
                navDrawerViewmodel = navigationDrawerViewModel,
                inventaireViewModel= proInventoryViewModels.inventaireViewModel,
                mainViewModel = mainViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getProInventoryDataViewModel= getProInventoryDataViewModel,
                selectArtInventoryVM = selectArtInventoryVM,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                getSharedDataViewModel = getSharedDataViewModel,
                syncProcaisseViewModels = syncProcaisseViewModels,
                syncSharedViewModels = syncSharedViewModels,
                syncInventoryViewModel = syncInventoryViewModel,
                sunmiPrintManager = sunmiPrintManager,
                inventairePrintViewModel = hiltViewModel<InventairePrintViewModel>(),
                settingViewModel = settingVM
            )
        }

        composable<InventoryInventaireDetailRoute> {
            InventaireDetailPane(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                networkViewModel = networkViewModel,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                dataViewModel = dataViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                inventaireViewModel= proInventoryViewModels.inventaireViewModel,
                mainViewModel = mainViewModel,
                selectArtInventoryVM = selectArtInventoryVM,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                settingViewModel = settingVM,
                sunmiPrintManager = sunmiPrintManager,
                inventairePrintViewModel = hiltViewModel<InventairePrintViewModel>()
            )
        }

        composable<InventoryInventaireAddRoute> {
            InventaireAddScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                barCodeViewModel = barCodeViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                inventaireViewModel = proInventoryViewModels.inventaireViewModel,
                mainViewModel = mainViewModel,
                selectArtNoCalculVM = selectArtInventoryVM,
                settingViewModel = settingVM
            )
        }

        composable<InventoryBonTransfertRoute> {
            BonTransfertScreen(
                navigate = { navigate(it) },
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                navDrawerViewmodel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                mainViewModel = mainViewModel,
                barCodeViewModel =barCodeViewModel,
                bonTransfertViewModel = proInventoryViewModels.bonTransfertViewModel,
                selectArtInventoryVM =selectArtInventoryVM,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,

                syncInventoryViewModel = syncInventoryViewModel,
                syncProcaisseViewModels = syncProcaisseViewModels,
                syncSharedViewModels = syncSharedViewModels,
                settingViewModel = settingVM,
                sunmiPrintManager = sunmiPrintManager,
                bonTransfertPrintViewModel = hiltViewModel<BonTransfertPrintViewModel>()
            )
        }
        composable<InventoryBonTransfertDetailRoute> {
            BonTranfertDetailPane(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                bonTransfertViewModel = proInventoryViewModels.bonTransfertViewModel,
                barCodeViewModel= barCodeViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                selectArtInventoryVM = selectArtInventoryVM,
                settingViewModel = settingVM,
                sunmiPrintManager = sunmiPrintManager,
                bonTransfertPrintViewModel = hiltViewModel<BonTransfertPrintViewModel>()
            )
        }

        composable<InventoryBonTransfertAddRoute> {
            BonTranfertAddScreen(
                navigate = { navigate(it) },
                popBackStack = { popBackStack() },
                bonTransfertViewModel = proInventoryViewModels.bonTransfertViewModel,
                barCodeViewModel= barCodeViewModel,
                dataViewModel = dataViewModel,
                networkViewModel = networkViewModel,
                mainViewModel = mainViewModel,
                navigationDrawerViewModel = navigationDrawerViewModel,
                selectArtNoCalculVM = selectArtInventoryVM,
                settingViewModel = settingVM
            )
        }




        composable<InventoryTicketRayonRoute> {
            TicketRayonScreen(
                networkViewModel = networkViewModel,
                navigate = { navigate(it) },
                dataViewModel = dataViewModel,
                barCodeViewModel = barCodeViewModel,
                navDrawerViewmodel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                ticketRayonViewModel = ticketRayonViewModel,
                mainViewModel= mainViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                printViewModel = printViewModel,
                bluetoothVM = bluetoothVM,
                syncProcaisseViewModels = syncProcaisseViewModels,
                syncSharedViewModels = syncSharedViewModels,
                syncInventoryViewModel = syncInventoryViewModel,
                settingViewModel = settingVM
            )
        }







        composable<ProductListRoute> {
            ArticlesScreen(
                navigate = { navigate(it) },
                navigationDrawerViewModel = navigationDrawerViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                articlesViewModel = articlesViewModel,
                articleTxtValidViewModel = articleTxtValidViewModel,
                barCodeViewModel = barCodeViewModel,
                getSharedDataViewModel = getSharedDataViewModel,
                from = PRO_INVENTORY,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                networkViewModel = networkViewModel,
                dataViewModel = dataViewModel,
                mainViewModel = mainViewModel,
                syncProcaisseViewModels = syncProcaisseViewModels,
                syncSharedViewModels = syncSharedViewModels,
                syncInventoryViewModel = syncInventoryViewModel,
                settingViewModel = settingVM
            )
        }

        composable<ProInventorySyncRoute> {
            ProInventorySyncScreen(
                navigate = { navigate(it) },
                navigatePopUpTo = { route: String, popUpTo: String, isInclusive: Boolean ->
                    navigatePopUpTo(route, popUpTo, isInclusive)
                },
                popBackStack = { popBackStack() },
                navigateUp = { navigateUp() },
                networkViewModel = networkViewModel,
                syncSharedViewModels = syncSharedViewModels,
                dataViewModel = dataViewModel,
                syncInventoryViewModel = syncInventoryViewModel
            )

        }



    }
}
