package com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.screens

import NavDrawer
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.adaptive.ExperimentalMaterial3AdaptiveApi
import androidx.compose.material3.adaptive.layout.AnimatedPane
import androidx.compose.material3.adaptive.layout.ListDetailPaneScaffoldRole
import androidx.compose.material3.adaptive.navigation.NavigableListDetailPaneScaffold
import androidx.compose.material3.adaptive.navigation.rememberListDetailPaneScaffoldNavigator
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_no_calcul.SelectArticleNoCalculViewModel
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.InventoryInventaireDetailRoute
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.core.print.sunmi.SunmiPrintManager
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.view_model.InventairePrintViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_inventory.inventaire.ui.InventaireViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch


@OptIn(ExperimentalMaterial3AdaptiveApi::class)
@Composable
fun InventaireScreen(
    navigate: (route: Any) -> Unit,
    navDrawerViewmodel: NavigationDrawerViewModel,
    inventaireViewModel: InventaireViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel : MainViewModel,
    selectArtInventoryVM : SelectArticleNoCalculViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,

    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    settingViewModel: SettingViewModel,
    sunmiPrintManager: SunmiPrintManager,
    inventairePrintViewModel: InventairePrintViewModel
) {
    val uiWindowState = settingViewModel.uiWindowState
    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)
    val navigator = rememberListDetailPaneScaffoldNavigator<Any>()
    val scope = rememberCoroutineScope()
    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerViewmodel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncProcaisseViewModels = syncProcaisseViewModels,
        syncSharedViewModels = syncSharedViewModels,
        syncInventoryViewModel = syncInventoryViewModel,
        settingViewModel = settingViewModel
    ) {
        NavigableListDetailPaneScaffold(
            navigator = navigator,
            listPane = {
                InventaireListPane(
                    navigate = {
                        scope.launch {
                            if(it is InventoryInventaireDetailRoute) {
                                navigator.navigateTo(pane = ListDetailPaneScaffoldRole.Detail)
                            }
                            else navigate(it)
                        }

                    },
                    drawer = drawer,
                    networkViewModel = networkViewModel,
                    dataViewModel = dataViewModel,
                    barCodeViewModel =barCodeViewModel,
                    navDrawerViewmodel = navDrawerViewmodel,
                    mainViewModel = mainViewModel,
                    printViewModel = printViewModel,
                    bluetoothVM = bluetoothVM,
                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                    settingViewModel = settingViewModel,
                    inventaireViewModel= inventaireViewModel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    selectArtInventoryVM = selectArtInventoryVM,

                    getSharedDataViewModel = getSharedDataViewModel,
                    syncProcaisseViewModels = syncProcaisseViewModels,
                    syncSharedViewModels = syncSharedViewModels,
                    syncInventoryViewModel = syncInventoryViewModel,
                    sunmiPrintManager = sunmiPrintManager,
                    inventairePrintViewModel = inventairePrintViewModel
                )


            },
            detailPane = {
                AnimatedPane {
                   /* val state = inventaireViewModel.inventaireListstate.lists
                    val ligneInventaireState = getProInventoryDataViewModel.ligneInventaireState

                    if(ligneInventaireState.loading)
                        LottieAnim(lotti = R.raw.loading, size = 250.dp)

                   else if (state.isEmpty())
                        LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                    else*/
                        InventaireDetailPane(
                            navigate = { navigate(it) },
                            popBackStack = {
                                //   popBackStack()
                                scope.launch {
                                    navigator.navigateBack()
                                }
                            },
                            networkViewModel = networkViewModel,
                            bluetoothVM = bluetoothVM,
                            printViewModel = printViewModel,
                            dataViewModel =dataViewModel,
                            mainViewModel = mainViewModel,
                            navigationDrawerViewModel = navDrawerViewmodel,
                            settingViewModel = settingViewModel,
                            inventaireViewModel= inventaireViewModel,
                            selectArtInventoryVM = selectArtInventoryVM,
                            getProInventoryDataViewModel = getProInventoryDataViewModel,
                            sunmiPrintManager = sunmiPrintManager,
                            inventairePrintViewModel = inventairePrintViewModel
                        )
                }
            },
            /*  extraPane = {

                    }*/
        )
    }
}



