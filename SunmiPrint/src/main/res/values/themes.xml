<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.PrinterXSample" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">#FF47455C</item>
        <item name="colorPrimaryVariant">#FF47455C</item>
        <item name="colorOnPrimary">#FFFFFFFF</item>
        <!-- Secondary brand color. -->
        <item name="colorSecondary">#FFFF6000</item>
        <item name="colorSecondaryVariant">#FFFF6000</item>
        <item name="colorOnSecondary">@android:color/black</item>
        <!-- Status bar color. -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <!-- Customize your theme here. -->
        <item name="colorSurface">#EDEDF0</item>

        <item name="android:actionOverflowButtonStyle">@style/OverflowStyle</item>
    </style>

    <!-- Menu折叠时的展示样式 -->
    <style name="OverflowStyle" parent="Widget.AppCompat.ActionButton.Overflow">
        <item name="android:src">@drawable/ic_log</item>
        <item name="android:paddingEnd">20dp</item>
    </style>
</resources>