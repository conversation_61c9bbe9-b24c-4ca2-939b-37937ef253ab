<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="model"
            type="com.sunmi.samples.printerx.ui.vm.LabelViewModel" />
    </data>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingStart="32px"
            android:paddingTop="24px"
            android:paddingEnd="32px">

            <TextView
                android:id="@+id/label_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_label_count"
                app:layout_constraintBottom_toBottomOf="@id/label_count_input"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/label_count_input" />

            <EditText
                android:id="@+id/label_count_input"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="40px"
                android:inputType="number"
                android:text="@={model.count}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/label_count"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/label_test1"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{()->model.printLabel1()}"
                android:text="@string/text_label_test1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/label_count_input" />

            <TextView
                android:id="@+id/label_test2"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{()->model.printLabel2()}"
                android:text="@string/text_label_test2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/label_test1" />

            <TextView
                android:id="@+id/label_test3"
                android:layout_width="match_parent"
                android:layout_height="88px"
                android:layout_marginTop="24px"
                android:background="@drawable/bg_button"
                android:gravity="center"
                android:onClick="@{(view)->model.printLabel3(view)}"
                android:text="@string/text_label_test3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/label_test2" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</layout>